#! /usr/bin/env python3
# coding=utf-8
import asyncio
import gc
import json
import shutil
import signal
import ssl
import tempfile
import traceback
import uuid
from asyncio import create_task, CancelledError
from collections import defaultdict
from contextlib import asynccontextmanager
from contextvars import ContextVar
from dataclasses import dataclass, field

from itertools import islice
from logging import Logger
from pathlib import Path

from typing import Any, Dict, Union, Optional, Protocol, List, Callable, Generator

import aiomonitor
import pyarrow as pa
from pyarrow.lib import ArrowNotImplementedError, ArrowTypeError
from sqlalchemy.exc import InterfaceError
from bs4 import BeautifulSoup

import aiohttp
import os
import sys
from zoneinfo import ZoneInfo
import re

from dependency_injector.containers import DynamicContainer, DeclarativeContainer
from dependency_injector.wiring import inject, Provide
from num2words import num2words
import pandas as pd
import time
import logging
from enum import Enum

from pykeepass import PyKeePass
from rich.align import Align
from rich.layout import Layout

from rich.live import Live
from rich.panel import Panel
from rich.progress import Progress, BarColumn, TextColumn, SpinnerColumn, TimeElapsedColumn, MofNCompleteColumn, TaskID
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich import print as rprint
from rich.console import Console

from sqlalchemy import (
    event, inspect, case, cast, literal, text,
    null, exc, DDL, delete, and_, or_, bindparam
)

from sqlalchemy.dialects.postgresql import insert, dialect, TEXT, JSON

from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy.orm import aliased

from sqlalchemy.sql.ddl import CreateSchema

from sqlalchemy.sql.functions import func

from sqlalchemy_utils import LtreeType, database_exists, create_database

from sqlalchemy.sql import select, update

import sqlalchemy.exc

import numpy as np
import psutil

from dags.data_pipeline.database import get_model_by_name
from dags.data_pipeline.database.upsert_operations import upsert, upsert_async
from dags.data_pipeline.dataframe_utils.dataframe_debugger import quick_save_async
from dags.data_pipeline.jira.api_client import fetch_with_retries_post, fetch_with_retries_get, fetch_with_retries


from dags.data_pipeline.monitoring_integration_complete import  DatabaseTaskMonitor

from dags.data_pipeline.utilities.security import verify_password, generate_password, md5_hash
from dags.data_pipeline.dbmodels import log_entry

from datetime import datetime, timedelta, timezone

try:
    from dbmodels.base import Base
    from dbmodels.user import User
    from dbmodels.allboards import AllBoards
    from dbmodels.versions import Versions
    from dbmodels.worklog import WorkLog, DeletedWorklog
    from dbmodels.issue import (Issue, IssueLinks, IssueFields, IssueComments)
    from dbmodels.issueclassification import IssueClassification
    from dbmodels.changelog import ChangelogJSON
    from dbmodels.initiativeattribute import InitiativeAttribute
    from dbmodels.sprint import Sprint
    from dbmodels.rundetails import RunDetailsJira
    from dbmodels.mixins import HasPrivate

except ModuleNotFoundError:
    from .dbmodels.base import Base
    from .dbmodels.user import User
    from .dbmodels.allboards import AllBoards
    from .dbmodels.versions import Versions
    from .dbmodels.worklog import WorkLog
    from .dbmodels.issue import (Issue, IssueLinks, IssueFields, IssueComments)
    from .dbmodels.issueclassification import IssueClassification
    from .dbmodels.changelog import ChangelogJSON
    from .dbmodels.initiativeattribute import InitiativeAttribute
    from .dbmodels.sprint import Sprint
    from .dbmodels.rundetails import RunDetailsJira
    from .dbmodels.mixins import HasPrivate

try:
    from containers import (
        EntryDetails, LoggerContainer, KeePassContainer, JiraEntryDetailsContainer,
        QueueContainer, DatabaseSessionManagerContainer, ApplicationContainer, EnhancedApplicationContainer,
        IssueFieldsContainer, FieldNameExtractor, CircuitState, CoreSessionManagerContainer
)

    from priority_queue_system import priority_queue_manager, MessageType, PriorityMessage
except ModuleNotFoundError:
    from .containers import (
        EntryDetails, KeePassContainer, LoggerContainer,
        QueueContainer, DatabaseSessionManagerContainer, JiraEntryDetailsContainer, ApplicationContainer, EnhancedApplicationContainer,
        IssueFieldsContainer, FieldNameExtractor, CircuitState
    )
    from .priority_queue_system import priority_queue_manager, MessageType





# CREDITS: https://stackoverflow.com/questions/32107558/how-do-i-convert-numpy-nan-objects-to-sql-nulls
# This converts np.NAN to nulls.

# Example of an event listener for connection checkout
# Listener functions
# DO NOT DELETE
# @inject
# def before_checkout(
#         dbapi_connection, connection_record, pool,
# my_logger=Provide[LoggerContainer.logger]
# ):
#     my_logger.dataframe_utils(f"Connection {dbapi_connection} will be checked out from pool.")
#
# @inject
# def after_checkout(
#         dbapi_connection, connection_record,
# my_logger=Provide[LoggerContainer.logger]
# ):
#     my_logger.dataframe_utils(f"Connection {dbapi_connection} has been checked out from pool.")
#
#
# @inject
# def on_connection_invalidated(
#         dbapi_connection, connection_record, pool,
# my_logger=Provide[LoggerContainer.logger]
# ):
#     my_logger.dataframe_utils(f"Connection {dbapi_connection} has been invalidated.")
# DO NOT DELETE




# Source https://realpython.com/python-print/


suffixes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']


def humansize(nbytes):
    i = 0
    while nbytes >= 1024 and i < len(suffixes) - 1:
        nbytes /= 1024.
        i += 1
    f = ('%.2f' % nbytes).rstrip('0').rstrip('.')
    return '%s %s' % (f, suffixes[i])


def log_connector_attrs(name: str, session: aiohttp.client.ClientSession, my_logger: Logger|None = None):
    if my_logger:
        my_logger.debug(f"Connection attributes for {name}")
    else:
        print("Connection attributes for", name, end="\n\n")
    conn = session._connector
    if my_logger:
        my_logger.debug(f"_conns: {conn._conns}")
        my_logger.debug(f"_acquired: {conn._acquired}")
        my_logger.debug(f"_acquired_per_host: {conn._acquired_per_host}")
        my_logger.debug(f"_available_connections")
        for k in conn._acquired_per_host:
            my_logger.debug(f"{k} => {conn._available_connections(k)}")
    else:
        print("_conns:", conn._conns, end="\n\n")
        print("_acquired:", conn._acquired, end="\n\n")
        print("_acquired_per_host:", conn._acquired_per_host, end="\n\n")
        print("_available_connections:")
        for k in conn._acquired_per_host:
            print("\t", k, conn._available_connections(k))
        print("-" * 70, end="\n\n")


def reduce_mem_usage(df: pd.DataFrame, my_logger: Logger|None = None) -> pd.DataFrame:
    numerics = ['int16', 'int32', 'int64', 'float16', 'float32', 'float64']
    start_mem = df.memory_usage().sum() / 1024 ** 2
    for col in df.columns:
        col_type = df[col].dtypes
        if col_type in numerics:
            c_min = df[col].min()
            c_max = df[col].max()
            # Start addition
            if pd.isnull(df[col]).any():  # Handle NaNs
                continue
            # end addition

            if str(col_type)[:3] == 'int':
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    df[col] = df[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    df[col] = df[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    df[col] = df[col].astype(np.int32)
                # elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:
                #     df[col] = df[col].astype(np.int64)

            else:
                # Downcast floats
                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                    df[col] = df[col].astype(np.float16)
                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    df[col] = df[col].astype(np.float32)
                # else:
                #     df[col] = df[col].astype(np.float64)
        elif col_type == 'object':
            # Check if the column contains lists
            if df[col].apply(lambda x: isinstance(x, list)).any():
                continue  # Skip columns with lists

            if df[col].apply(lambda x: isinstance(x, dict)).any():
                continue  # Skip columns with dict (json)

            # Optimize categorical columns
            num_unique_values = len(df[col].unique())
            num_total_values = len(df[col])
            if num_unique_values / num_total_values < 0.5:
                df[col] = df[col].astype('category')

    end_mem = df.memory_usage().sum() / 1024 ** 2
    if isinstance(my_logger, Logger):
        my_logger.debug('Memory usage after optimization is: {:.2f} MB'.format(end_mem))
        my_logger.debug('Decreased by {:.1f}%'.format(100 * (start_mem - end_mem) / start_mem))

    return df


def cast_columns(df: pd.DataFrame, columns: list[str], dtype: Union[str, pd.Int64Dtype, pd.Float64Dtype]):
    """
    Cast specified columns in the DataFrame to the provided type if they exist.

    Args:
        df (pd.DataFrame): The DataFrame to modify.
        columns (list): List of column names to cast.
        dtype (str or pd.dtype): The type to cast the columns to. Can be 'datetime', 'Int64', 'Float64', etc.

    Returns:
        pd.DataFrame: The modified DataFrame with columns casted as specified.
    """
    # Select the columns that exist in the DataFrame
    existing_columns = [col for col in columns if col in df.columns]

    # Vectorized casting based on dtype
    if isinstance(dtype, (pd.Int64Dtype, pd.Float64Dtype)):
        df[existing_columns] = df[existing_columns].astype(dtype)
    elif dtype == 'datetime':
        df[existing_columns] = df[existing_columns].apply(pd.to_datetime, errors='coerce')
    elif dtype == "date":
        # Convert to datetime first, then extract only the date part
        df[existing_columns] = pd.to_datetime(df[existing_columns], errors='coerce').dt.date
    else:
        df[existing_columns] = df[existing_columns].astype(dtype)
    return df


def schema_check_create(schema_name: str):
    # Define the DDL for creating a schema if it doesn't exist
    create_schema_ddl_template = """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_namespace
                WHERE nspname = '%(schema)s'
            ) THEN
                CREATE SCHEMA %(schema)s;
            END IF;
        END $$;
        """

    create_schema_ddl = DDL(create_schema_ddl_template % {'schema': schema_name})

    def create_schema_if_not_exists(target, connection, **kwargs):
        connection.execute(create_schema_ddl, schema=schema_name)

    # Attach the DDL to the Base metadata's before_create event
    event.listen(Base.metadata, 'before_create', create_schema_if_not_exists)



def compile_query(query):
    """Via http://nicolascadou.com/blog/2014/01/printing-actual-sqlalchemy-queries"""
    compiler = query.compile if not hasattr(query, 'statement') else query.statement.compile
    return compiler(dialect=dialect(), compile_kwargs={"literal_binds": False})



async def detect_deadlocks(
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger|None = None
):
    """
    Detects deadlocks in the PostgreSQL database by analyzing pg_locks and pg_stat_activity.
    Returns a list of PIDs of transactions involved in deadlocks.
    """
    if isinstance(my_logger, Logger):
        my_logger.info(f"started detect_deadlocks")
    app_container.schema.override('public')

    try:

        async with app_container.database_rw().update_schema('public').async_session() as pg_async_session:

            query = text("""
                WITH blocked_locks AS (
                    SELECT
                        l1.locktype, l1.database, l1.relation, l1.page, l1.tuple,
                        l1.virtualxid, l1.transactionid, l1.classid, l1.objid, l1.objsubid,
                        l1.pid AS blocked_pid, l1.mode AS blocked_mode,
                        l2.pid AS blocking_pid, l2.mode AS blocking_mode
                    FROM
                        pg_locks l1
                    JOIN
                        pg_locks l2
                    ON
                        l1.locktype = l2.locktype
                        AND l1.database IS NOT DISTINCT FROM l2.database
                        AND l1.relation IS NOT DISTINCT FROM l2.relation
                        AND l1.page IS NOT DISTINCT FROM l2.page
                        AND l1.tuple IS NOT DISTINCT FROM l2.tuple
                        AND l1.virtualxid IS NOT DISTINCT FROM l2.virtualxid
                        AND l1.transactionid IS NOT DISTINCT FROM l2.transactionid
                        AND l1.classid IS NOT DISTINCT FROM l2.classid
                        AND l1.objid IS NOT DISTINCT FROM l2.objid
                        AND l1.objsubid IS NOT DISTINCT FROM l2.objsubid
                    WHERE
                        l1.granted = false AND l2.granted = true
                )
                SELECT DISTINCT
                    blocked_pid
                FROM
                    blocked_locks
                WHERE
                    blocked_pid <> blocking_pid;
            """)

            result = await pg_async_session.execute(query)
            rows = [row[0] for row in result.fetchall()]
            if isinstance(my_logger, Logger):
                my_logger.debug(f"rows returned = {rows}")
            return rows
    except exc.SQLAlchemyError as e:
        if isinstance(my_logger, Logger):
            my_logger.error(f"Error detecting deadlocks: {e}")
        return []



async def kill_transaction(
        pid: int,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger : Logger|None = None
):
    """
    Kills a transaction in PostgreSQL using pg_terminate_backend.
    """
    try:
        query = text(f"SELECT pg_terminate_backend(:pid)")
        async with app_container.database_rw().update_schema('public').async_session() as pg_async_session:
            await pg_async_session.execute(query, {'pid': pid})
        if isinstance(my_logger, Logger):
            my_logger.warning(f"Terminated transaction with PID: {pid}")
    except exc.SQLAlchemyError as e:
        if isinstance(my_logger, Logger):
            my_logger.error(f"Error terminating transaction {pid}: {e}")



async def monitor_deadlocks(
        interval: int = 5,
        my_logger: Logger|None = None

):
    """
    Monitors and resolves deadlocks periodically.
    """
    while True:
        # Detect deadlocked transactions
        deadlocked_pids = await detect_deadlocks()
        if deadlocked_pids:
            if isinstance(my_logger, Logger):
                my_logger.info(f"Detected deadlocks: {deadlocked_pids}")
            for pid in deadlocked_pids:
                # Terminate deadlocked transactions
                await kill_transaction(pid)
        await asyncio.sleep(interval)  # Wait before next check


# Placeholder - TaskLifecycleCoordinator moved earlier in file
class TaskLifecycleCoordinator:
    """
    Coordinates task lifecycle and graceful shutdown of monitors.

    This class uses asyncio.Condition for better coordination between
    task completion and monitor shutdown, replacing the previous manual
    cancellation approach with proper synchronization.
    """

    def __init__(self, logger: Logger):
        """Initialize the task lifecycle coordinator."""
        self.logger = logger
        self._condition = asyncio.Condition()
        self._active_tasks: set[str] = set()
        self._completed_tasks: set[str] = set()
        self._shutdown_requested: bool = False
        # self._monitors: set = set()
        self._monitors: Dict[str, asyncio.Task] = {}
        self._task_counter = 0

    async def register_task(self, task_name: str) -> str:
        """
        Register a new task for tracking.

        Args:
            task_name: Name of the task to register

        Returns:
            str: Unique task ID for tracking
        """
        async with self._condition:
            self._task_counter += 1
            task_id = f"{task_name}_{self._task_counter}"
            self._active_tasks.add(task_id)
            self.logger.debug(f"Registered task: {task_id}")
            return task_id

    async def mark_task_completed(self, task_id: str, result: Any = None):
        """
        Mark a task as completed and notify waiting monitors.

        Args:
            task_id: ID of the completed task
            result: Task result (optional)
        """
        async with self._condition:
            if task_id in self._active_tasks:
                self._active_tasks.remove(task_id)
                self._completed_tasks.add(task_id)
                self.logger.debug(f"Task completed: {task_id}, result: {result}")

                # Check if all tasks are completed
                if not self._active_tasks:
                    self.logger.info("All tasks completed, initiating monitor shutdown")
                    self._shutdown_requested = True

                # Notify all waiting monitors
                self._condition.notify_all()

    async def mark_task_failed(self, task_id: str, error: Exception):
        """
        Mark a task as failed and notify waiting monitors.

        Args:
            task_id: ID of the failed task
            error: Exception that caused the failure
        """
        async with self._condition:
            if task_id in self._active_tasks:
                self._active_tasks.remove(task_id)
                self._completed_tasks.add(task_id)  # Failed tasks are still "completed"
                self.logger.error(f"Task failed: {task_id}, error: {error}")

                # Check if all tasks are completed (including failed ones)
                if not self._active_tasks:
                    self.logger.info("All tasks completed (some failed), initiating monitor shutdown")
                    self._shutdown_requested = True

                # Notify all waiting monitors
                self._condition.notify_all()

    async def register_monitor(self, monitor_task: asyncio.Task, monitor_name: str):
        """
        Register a monitor task for lifecycle management.

        Args:
            monitor_task: The monitor task to register
            monitor_name: Name of the monitor for logging
        """
        monitor_info = {
            'task': monitor_task,
            'name': monitor_name,
            'registered_at': asyncio.get_event_loop().time()
        }
        # self._monitors.add(frozenset(monitor_info.items()))
        self._monitors[monitor_name] = monitor_task
        self.logger.debug(f"Registered monitor: {monitor_name}")

    async def wait_for_all_tasks_completion(self, timeout: Optional[float] = None):
        """
        Wait for all registered tasks to complete.

        Args:
            timeout: Maximum time to wait (None for no timeout)
        """
        async with self._condition:
            if self._active_tasks:
                try:
                    await asyncio.wait_for(
                        self._condition.wait_for(lambda: not self._active_tasks),
                        timeout=timeout
                    )
                except asyncio.TimeoutError:
                    self.logger.warning(f"Timeout waiting for task completion. Active tasks: {self._active_tasks}")

        # Shutdown monitors after all tasks complete
        await self.shutdown_monitors()

    async def shutdown_monitors(self, timeout: float = 5.0):
        """
        Gracefully shutdown all registered monitors.

        Args:
            timeout: Maximum time to wait for monitor shutdown
        """
        if not self._monitors:
            return

        self.logger.info(f"Shutting down {len(self._monitors)} monitors")

        # Cancel all monitors
        active_monitors = []
        for monitor_name, monitor_task in list(self._monitors.items()):
            if not monitor_task.done():
                self.logger.debug(f"Cancelling monitor: {monitor_name}")
                monitor_task.cancel()
                active_monitors.append(monitor_task)
            else:
                self.logger.debug(f"Monitor already completed: {monitor_name}")

        # Wait for monitors to handle cancellation gracefully
        if active_monitors:
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*active_monitors, return_exceptions=True),
                    timeout=timeout
                )
                # Log results for debugging
                cancelled_count = sum(1 for r in results if isinstance(r, asyncio.CancelledError))
                error_count = sum(
                    1 for r in results if isinstance(r, Exception) and not isinstance(r, asyncio.CancelledError))

                self.logger.info(f"Monitor shutdown complete - Cancelled: {cancelled_count}, Errors: {error_count}")
            except asyncio.TimeoutError:
                self.logger.warning(f"Timeout waiting for monitor shutdown after {timeout}s")
                for monitor_task in active_monitors:
                    if not monitor_task.done():
                        self.logger.warning(f"Force cancelling unresponsive monitor task")
                        monitor_task.cancel()
            # Clear the monitors dict
            self._monitors.clear()


    def is_shutdown_requested(self) -> bool:
        """Check if shutdown has been requested."""
        return self._shutdown_requested

    def get_active_task_count(self) -> int:
        """Get the number of active tasks."""
        return len(self._active_tasks)

    def get_completed_task_count(self) -> int:
        """Get the number of completed tasks."""
        return len(self._completed_tasks)

    async def force_shutdown(self):
        """Force immediate shutdown of all monitors and reset state."""
        self.logger.warning("Force shutdown requested")

        # Cancel all monitors immediately
        for monitor_name, monitor_task in self._monitors.items():
            if not monitor_task.done():
                self.logger.debug(f"Force cancelling monitor: {monitor_name}")
                monitor_task.cancel()

        # Clear all state
        self._monitors.clear()
        self._active_tasks.clear()
        self._shutdown_requested = True

        # Notify any waiting tasks
        async with self._condition:
            self._condition.notify_all()


@inject
async def get_jira_users(
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> str:

    try:
        asyncio.current_task().set_name("get_jira_users")

        my_logger.info("get_jira_users called")
        tasks = []
        endpoint = "/rest/api/3/users"
        url = f"{jira_entry.url}{endpoint}"
        timeout = aiohttp.ClientTimeout(total=300)
        connector = aiohttp.TCPConnector(
            limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
            ssl=ssl.create_default_context()
        )

        async with aiohttp.ClientSession(
            headers=jira_entry.custom_properties,
            timeout=timeout,
            connector=connector,
            raise_for_status=True
        ) as http_session:
            for i in range(3):
                payload_dict = {'startAt': i * 1000, 'maxResults': 1000}
                tasks.append(fetch_with_retries_get(http_session, url, payload_dict))
            responses = await asyncio.gather(*tasks, return_exceptions=True)
        # my_logger.debug(f"responses: {responses}")
        successful_responses = [resp["result"] for resp in responses if isinstance(resp, dict) and resp.get("success")]
        failures = [resp for resp in responses if not (isinstance(resp, dict) and resp.get("success"))]
        FAILURE_THRESHOLD = max(1, int(0.5 * len(tasks)))
        my_logger.debug(f"FAILURE_THRESHOLD: {FAILURE_THRESHOLD}, failures = {len(failures)}")
        if len(failures) > FAILURE_THRESHOLD:
            my_logger.critical(f"Too many user fetch failures ({len(failures)}/{len(tasks)}), triggering shutdown.")
            global commit_transaction
            async with lock:
                commit_transaction = False
            raise RuntimeError("Graceful shutdown: too many user fetch failures")

        my_logger.debug(f"# of successful_responses: {[len(sublist) for sublist in successful_responses]}")
        my_logger.debug(f"# of failures: {len(failures)}")

        if not failures:
            responses = [element for innerList in successful_responses for element in innerList]
            df = pd.DataFrame(responses)
            for col in ["self", "avatarUrls"]:
                if col in df.columns:
                    df.drop(columns=[col])
            return_string = f"+{df.shape[0]}"
            app_container.schema.override('public')
            with app_container.database_rw().session() as pg_session:
                try:
                    upsert(
                        pg_session, User, df,
                        on_conflict_update=True
                    )
                    pg_session.commit()
                except ConnectionResetError as e:
                    my_logger.error(f"Database connection was reset: {e}")
                    return_string = "DB EXCEPTION"
                    raise e
        else:
            for failure in failures:
                my_logger.error(f"Task failed with exception: {failure}")
            return_string = "UNKNOWN EXCEPTION"

    except Exception as e:
        return_string = "FAILED"
        handle_exception(e)
        raise e

    return return_string


@inject
async def get_all_jira_boards(
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],
        progress_callback=None
):
    ret_string = ""
    my_logger.debug("Started get_all_jira_boards")
    tasks = []
    asyncio.current_task().set_name("get_all_jira_boards")

    # Register for monitoring
    global_async_process_tracker.register_process("get_all_jira_boards", "task", "system")

    endpoint = "/rest/agile/1.0/board/"
    url = f"{jira_entry.url}{endpoint}"
    app_container.schema.override('public')
    my_logger.debug(f"schema is set to: {app_container.schema()}")
    timeout = aiohttp.ClientTimeout(total=300)
    connector = aiohttp.TCPConnector(
        limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
        ssl=ssl.create_default_context()
    )
    async with aiohttp.ClientSession(
            headers=jira_entry.custom_properties,
            timeout=timeout,
        connector=connector
    ) as http_session:
        for i in range(2):
            payload_dict = {'startAt': i * 50, 'maxResults': 50}
            tasks.append(fetch_with_retries_get(http_session, url, payload_dict))
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        successful_responses = [resp["result"] for resp in responses if isinstance(resp, dict) and resp.get("success")]
        failures = [resp for resp in responses if not (isinstance(resp, dict) and resp.get("success"))]
        FAILURE_THRESHOLD = max(1, int(0.5 * len(tasks)))
        my_logger.debug(f"FAILURE_THRESHOLD: {FAILURE_THRESHOLD}, failures = {len(failures)}")
        if len(failures) > FAILURE_THRESHOLD:
            my_logger.critical(f"Too many board fetch failures ({len(failures)}/{len(tasks)}), triggering shutdown.")
            global commit_transaction
            async with lock:
                commit_transaction = False
            raise RuntimeError("Graceful shutdown: too many board fetch failures")

        my_logger.debug(f"# of successful_responses: {[len(sublist) for sublist in successful_responses]}")
        my_logger.debug(f"# of failures: {len(failures)}")

        if not failures:
            df = pd.DataFrame(successful_responses)
            df = df[df['values'].apply(lambda x: len(x) > 0)]
            df = df.explode(column="values").reset_index()
            df = pd.json_normalize(df["values"])
            df.drop(columns=["self", "location.name", "location.avatarURI"], inplace=True)
            df.rename(columns=lambda x: x.replace('location.', ''), inplace=True)
            return_string = f"+{df.shape[0]}"
            df['projectId'] = df['projectId'].astype(pd.Int64Dtype())
            df['userId'] = df['userId'].astype(pd.Int64Dtype())
            ret_string = f"+{df.shape[0]}"

            # Update progress tracking
            global_async_process_tracker.update_process("get_all_jira_boards", "running", df.shape[0])
            if progress_callback:
                progress_callback(50, f"Processing {df.shape[0]} boards")

            async with app_container.database_rw().async_session() as pg_async_session:
                try:
                    await upsert_async(pg_async_session, AllBoards, df)
                    await pg_async_session.commit()

                    # Mark as completed
                    global_async_process_tracker.update_process("get_all_jira_boards", "completed", df.shape[0])
                    if progress_callback:
                        progress_callback(100, f"Completed: {df.shape[0]} boards")

                except Exception as e:
                    global_async_process_tracker.update_process("get_all_jira_boards", "failed")
                    handle_exception(e)
            return return_string
        else:
            for failure in failures:
                my_logger.error(f"Task failed with exception: {failure}")
            return "UNKNOWN Exception"


async def delete_in_batches(session: AsyncSession, ids_to_delete, batch_size: int = 32_000):
    # Split the IDs into smaller chunks
    for i in range(0, len(ids_to_delete), batch_size):
        chunk = ids_to_delete[i:i + batch_size]
        delete_stmt = delete(WorkLog).where(WorkLog.id.in_(chunk))
        await session.execute(delete_stmt)
    await session.commit()


########
@inject
async def get_deleted_worklog(
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],
):
    return_string = ""
    try:
        task = asyncio.current_task()
        task.set_name("deleted_worklogs")
        endpoint = "/rest/api/3/myself"
        url = f"{jira_entry.url}{endpoint}"
        my_logger.debug(f"module name = {__name__}")
        timeout = aiohttp.ClientTimeout(total=300)
        connector = aiohttp.TCPConnector(
            limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
            ssl=ssl.create_default_context()
        )
        async with aiohttp.ClientSession(
            headers=jira_entry.custom_properties, timeout=timeout, connector=connector,
            raise_for_status=True
        ) as http_session:
            response = await fetch_with_retries_get(http_session, url)
            my_logger.debug(f"{response}")
            time_zone = ZoneInfo(response['result']['timeZone'])
            # Set local_datetime to current time
            last_run_time = datetime.now(tz=time_zone)

            async with app_container.database_rw().async_session() as pg_async_session:
                stmt = select(RunDetailsJira.last_run).filter(RunDetailsJira.topic == "DeletedWorklog")
                try:
                    result = await pg_async_session.execute(stmt)
                    local_datetime = result.scalar_one()
                    my_logger.debug(f"local datetime returned: {local_datetime}")
                except sqlalchemy.exc.NoResultFound:
                    # Create a datetime object with the specified date, time, and timezone
                    local_datetime = datetime(
                        year=2010, month=1, day=1, hour=0, minute=0, second=0, tzinfo=time_zone
                    )
                    my_logger.info(f"no rows found in {RunDetailsJira.__table__}. default time: {local_datetime}")

                since = int(
                    (
                            local_datetime - datetime(1970, 1, 1, tzinfo=time_zone)
                    ).total_seconds() * 1000)
                last_page = False

                endpoint = "/rest/api/3/worklog/deleted"
                url = f"{jira_entry.url}{endpoint}"

                page_number = 0
                params = {'since': since}
                while not last_page:
                    my_logger.debug(f"page number {(page_number := page_number + 1)}")
                    response = await fetch_with_retries_get(http_session, url, params=params)

                    if response.get("success"):
                        result = response.get('result', {})
                        last_page = result.get('lastPage', True)  # Default to True to exit loop on error
                        df = pd.DataFrame(result.get('values', []))

                        # Only process next page if we have more data and the request was successful
                        if not last_page:
                            url = result.get('nextPage', url)  # Keep current URL if nextPage not available
                    else:
                        # Log error and break the loop
                        my_logger.error(f"Failed to fetch deleted worklog: {response.get('exception')}")
                        break

                    params = None

                    await upsert_async(
                        pg_async_session, DeletedWorklog, df, on_conflict_update=False
                    )

                run_details_jira = RunDetailsJira(topic="DeletedWorklog", last_run=last_run_time)
                await pg_async_session.merge(run_details_jira)
                await pg_async_session.commit()

    except Exception as e:
        return_string = "FAILED"
        handle_exception(e)

    return return_string


########


@inject
async def get_fields(
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> str:
    task = asyncio.current_task()
    task.set_name("get_fields")
    ret_value = "Not Initialized"
    try:
        endpoint = "/rest/api/3/field"
        url = f"{jira_entry.url}{endpoint}"
        timeout = aiohttp.ClientTimeout(total=300)
        connector = aiohttp.TCPConnector(
            limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
            ssl=ssl.create_default_context()
        )
        async with aiohttp.ClientSession(
            headers=jira_entry.custom_properties, timeout=timeout,
            connector=connector, raise_for_status=True
        ) as http_session:
            response = await fetch_with_retries_get(http_session, url)

            for node in response["result"]:
                for key, value in node.items():
                    if key == 'schema':
                        node['schema'] = [node['schema']]

            df = pd.json_normalize(response["result"])
            df['schema'] = df['schema'].apply(lambda x: x[0] if isinstance(x, list) and len(x) > 0 else {})
        ret_value = f"+{df.shape[0]}"
        my_logger.info(f"Fields returned: {df.shape[0]}")

        app_container.schema.override('public')
        with app_container.database_rw().session() as pg_session:
            upsert(pg_session, IssueFields, df, )
            pg_session.commit()
            my_logger.debug("DB commit done!!!")
    except Exception as e:
        ret_value = "FAILED"
        handle_exception(e)

    return ret_value


# Function to parse Jira's timestamp format
def parse_jira_timestamp(timestamp_str):
    # Example timestamp: "2024-07-01T18:40:35.617+0530"
    try:
        return datetime.strptime(timestamp_str, '%Y-%m-%dT%H:%M:%S.%f%z')
    except ValueError:
        return datetime.strptime(timestamp_str, '%Y-%m-%dT%H:%M:%S%z')


# Function to convert list of dicts to a single dict
def list_of_dicts_to_dict(list_of_dicts):
    if list_of_dicts:
        return list_of_dicts[0]
    return {}

# Configuration and Data Classes
@dataclass
class SprintFetchConfig:
    max_results_per_page: int = 50
    max_concurrent_requests: int = 5
    max_retries: int = 5

@dataclass
class SprintFetchResult:
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[Exception] = None
    sprint_id: Optional[int] = None
    request_type: str = "unknown"

# Protocols (Interfaces)
class HttpClientProtocol(Protocol):
    async def fetch_with_retries(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        ...

class DataProcessorProtocol(Protocol):
    def process_sprint_data(self, responses: List[Dict[str, Any]]) -> pd.DataFrame:
        ...


# Queue-based HTTP Client that leverages your existing retry logic
class QueuedHttpClient:
    def __init__(self, session: aiohttp.ClientSession, config: SprintFetchConfig):
        self.session = session
        self.config = config
        self.request_queue = asyncio.Queue()
        self.response_queue = asyncio.Queue()
        self._workers_started = False
        self._active_workers = 0

    async def start_workers(self):
        """Start worker tasks for processing requests"""
        if self._workers_started:
            return

        for worker_id in range(self.config.max_concurrent_requests):
            asyncio.create_task(self._worker(worker_id))
        self._workers_started = True

    async def _worker(self, worker_id: int):
        """Worker coroutine that processes requests from the queue using your existing retry logic"""
        while True:
            try:
                self._active_workers += 1
                request_data = await self.request_queue.get()

                if request_data is None:  # Sentinel to stop worker
                    break

                # Use your existing fetch_with_retries function
                result = await fetch_with_retries(
                    session=self.session,
                    method=request_data.get('method', 'GET'),
                    url=request_data['url'],
                    params=request_data.get('params'),
                    json_payload=request_data.get('json_payload'),
                    retries=self.config.max_retries
                )

                # Transform result to our format
                fetch_result = SprintFetchResult(
                    success=result.get('success', False) if result else False,
                    data=result.get('result') if result and result.get('success') else None,
                    error=Exception(result.get('exception', 'Unknown error')) if result and not result.get(
                        'success') else None,
                    sprint_id=request_data.get('sprint_id'),
                    request_type=request_data.get('request_type', 'unknown')
                )

                await self.response_queue.put(fetch_result)

            except Exception as e:
                # Handle any unexpected errors
                await self.response_queue.put(SprintFetchResult(
                    success=False,
                    error=e,
                    sprint_id=request_data.get('sprint_id') if 'request_data' in locals() else None,
                    request_type=request_data.get('request_type',
                                                  'unknown') if 'request_data' in locals() else 'unknown'
                ))
            finally:
                self._active_workers -= 1
                if 'request_data' in locals():
                    self.request_queue.task_done()

    async def fetch_multiple(self, requests: List[Dict[str, Any]]) -> List[SprintFetchResult]:
        """Queue multiple requests and return results"""
        await self.start_workers()

        # Queue all requests
        for request in requests:
            await self.request_queue.put(request)

        # Collect results
        results = []
        for _ in range(len(requests)):
            result = await self.response_queue.get()
            results.append(result)

        return results

    async def shutdown(self):
        """Gracefully shutdown workers"""
        if not self._workers_started:
            return

        # Wait for queue to be empty
        await self.request_queue.join()

        # Send sentinel values to stop workers
        for _ in range(self.config.max_concurrent_requests):
            await self.request_queue.put(None)

        # Wait for workers to finish
        while self._active_workers > 0:
            await asyncio.sleep(0.1)


# Data Processing Classes
class SprintDataProcessor:
    @staticmethod
    def process_sprint_responses(responses: List[SprintFetchResult], max_loop_count: int) -> pd.DataFrame:
        """Process sprint API responses into DataFrame"""
        sprint_responses = [r for r in responses if r.request_type == 'sprint' and r.success and r.data]

        if not sprint_responses:
            return pd.DataFrame()

        # Process sprint data
        sprint_values = []
        for response in sprint_responses[:max_loop_count]:
            if 'values' in response.data:
                sprint_values.extend(response.data['values'])

        if not sprint_values:
            return pd.DataFrame()

        df = pd.DataFrame(sprint_values)
        if 'self' in df.columns:
            df.drop(columns=['self'], inplace=True)

        return df

    @staticmethod
    def process_velocity_data(responses: List[SprintFetchResult]) -> pd.DataFrame:
        """Process velocity statistics into DataFrame"""
        velocity_responses = [r for r in responses if r.request_type == 'velocity' and r.success and r.data]

        if not velocity_responses:
            return pd.DataFrame()

        velocity_data = velocity_responses[0].data
        if 'velocityStatEntries' not in velocity_data:
            return pd.DataFrame()

        df_velocity = pd.DataFrame.from_dict(
            velocity_data["velocityStatEntries"],
            orient="index"
        ).reset_index()
        df_velocity['index'] = df_velocity['index'].astype('int64')

        return df_velocity

    @staticmethod
    def process_burndown_data(responses: List[SprintFetchResult]) -> pd.DataFrame:
        """Process burndown chart data into DataFrame"""
        burndown_responses = [r for r in responses if r.request_type == 'burndown' and r.success and r.data]

        if not burndown_responses:
            return pd.DataFrame()

        successful_data = []
        for response in burndown_responses:
            response.data['sprint_id'] = response.sprint_id
            successful_data.append(response.data)

        df_burndown = pd.json_normalize(successful_data)

        # Process complex columns
        complex_columns = ['changes', 'issueToParentKeys', 'issueToSummary',
                           'workRateData', 'openCloseChanges']
        for col in complex_columns:
            if col in df_burndown.columns:
                df_burndown[col] = df_burndown[col].map(lambda x: x[0] if x else {})

        return df_burndown


# Request Builders
class JiraRequestBuilder:
    def __init__(self, jira_entry, config: SprintFetchConfig):
        self.jira_entry = jira_entry
        self.config = config

    def build_sprint_requests(self, board_id: int, max_loop_count: int) -> List[Dict[str, Any]]:
        """Build requests for sprint data"""
        requests = []
        url = f"{self.jira_entry.url}/rest/agile/1.0/board/{board_id}/sprint"

        for i in range(max_loop_count):
            requests.append({
                'method': 'GET',
                'url': url,
                'params': {
                    'maxResults': self.config.max_results_per_page,
                    'startAt': i * self.config.max_results_per_page
                },
                'request_type': 'sprint'
            })

        return requests

    def build_velocity_request(self, board_id: int) -> Dict[str, Any]:
        """Build request for velocity data"""
        url = f"{self.jira_entry.url}/rest/greenhopper/1.0/rapid/charts/velocity"
        return {
            'method': 'GET',
            'url': url,
            'params': {'rapidViewId': board_id},
            'request_type': 'velocity'
        }

    def build_burndown_requests(self, board_id: int, sprint_ids: List[int]) -> List[Dict[str, Any]]:
        """Build requests for burndown data"""
        requests = []
        url = f"{self.jira_entry.url}/rest/greenhopper/1.0/rapid/charts/scopechangeburndownchart"

        for sprint_id in sprint_ids:
            requests.append({
                'method': 'GET',
                'url': url,
                'params': {'rapidViewId': board_id, 'sprintId': sprint_id},
                'sprint_id': sprint_id,
                'request_type': 'burndown'
            })

        return requests


# Database Operations (same as before)
class SprintDatabaseManager:
    @staticmethod
    async def upsert_sprints(session, df: pd.DataFrame) -> str:
        """Upsert sprint data to database"""
        if df.empty:
            return "+0"

        # Cast date columns
        df = SprintDatabaseManager._cast_columns(
            df, ['startTime', 'endTime', 'completeTime', 'now'], pd.Int64Dtype()
        )
        df = SprintDatabaseManager._cast_columns(
            df, ['startDate', 'endDate', 'completeDate', 'createdDate'], "datetime"
        )

        # Remove unnecessary columns
        columns_to_drop = ['index', 'sprint_id']
        df.drop(columns=[col for col in columns_to_drop if col in df.columns], inplace=True)

        result = await upsert_async(session, Sprint, df, on_conflict_update=True)
        return f"+{df.shape[0]}"

    @staticmethod
    def _cast_columns(df: pd.DataFrame, columns: List[str], dtype) -> pd.DataFrame:
        """Helper method to cast columns to specified dtype"""
        for col in columns:
            if col in df.columns:
                if dtype == 'datetime':
                    df[col] = pd.to_datetime(df[col], errors='coerce')
                else:
                    df[col] = df[col].astype(dtype)
        return df


# Main Service Class
class SprintDetailsService:
    def __init__(self, config: SprintFetchConfig = None):
        self.config = config or SprintFetchConfig()
        self.data_processor = SprintDataProcessor()
        self.db_manager = SprintDatabaseManager()

    async def get_board_ids(self, session, project_key: str) -> List[int]:
        """Get scrum board IDs for the project"""
        stmt = select(AllBoards.id).filter(
            (AllBoards.projectKey.in_([project_key.upper()])) &
            (AllBoards.type.in_(['scrum']))
        )
        result = await session.execute(stmt)
        return result.scalars().all()

    async def fetch_sprint_data_for_board(self, http_client: QueuedHttpClient,
                                          request_builder: JiraRequestBuilder,
                                          board_id: int, max_loop_count: int,
                                          my_logger) -> pd.DataFrame:
        """Fetch and process all sprint data for a single board"""

        # Build initial requests (sprint data + velocity)
        sprint_requests = request_builder.build_sprint_requests(board_id, max_loop_count)
        velocity_request = request_builder.build_velocity_request(board_id)
        initial_requests = sprint_requests + [velocity_request]

        # Fetch sprint and velocity data
        my_logger.debug(f"Fetching {len(initial_requests)} initial requests for board {board_id}")
        responses = await http_client.fetch_multiple(initial_requests)

        # Log any failures
        failed_responses = [r for r in responses if not r.success]
        if failed_responses:
            my_logger.warning(f"Failed {len(failed_responses)} initial requests for board {board_id}")

        # Process basic sprint data
        df_sprints = self.data_processor.process_sprint_responses(responses, max_loop_count)
        if df_sprints.empty:
            my_logger.warning(f"No sprint data found for board {board_id}")
            return pd.DataFrame()

        # Process velocity data
        df_velocity = self.data_processor.process_velocity_data(responses)
        if not df_velocity.empty:
            df_sprints = df_sprints.merge(df_velocity, how='left', left_on='id', right_on='index')
            my_logger.debug(f"Merged velocity data for {len(df_velocity)} sprints")

        # Fetch burndown data for each sprint
        sprint_ids = df_sprints['id'].tolist()
        if sprint_ids:
            my_logger.debug(f"Fetching burndown data for {len(sprint_ids)} sprints")
            burndown_requests = request_builder.build_burndown_requests(board_id, sprint_ids)
            burndown_responses = await http_client.fetch_multiple(burndown_requests)

            # Log burndown failures
            failed_burndown = [r for r in burndown_responses if not r.success]
            if failed_burndown:
                my_logger.warning(f"Failed {len(failed_burndown)} burndown requests for board {board_id}")

            # Process burndown data
            df_burndown = self.data_processor.process_burndown_data(burndown_responses)
            if not df_burndown.empty:
                df_sprints = df_sprints.merge(df_burndown, how='left', left_on='id', right_on='sprint_id')
                my_logger.debug(f"Merged burndown data for {len(df_burndown)} sprints")

        return df_sprints


@inject
async def get_sprint_details(
        project_key: str, max_loop_count: int = 3,
        config: SprintFetchConfig = None,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        my_logger: Logger = Provide[LoggerContainer.logger],
        progress_callback=None
):
    """
    Fetch sprint details for a project using queued requests with your existing retry logic.

    Args:
        project_key: The project key to fetch sprints for
        max_loop_count: Maximum number of pages to fetch (defaults to 3)
        config: Configuration for concurrency and retries
        progress_callback: Optional callback for progress updates

    Returns:
        String indicating success (+count) or failure (FAILED)
    """
    task_name = f"get_sprint_details_{project_key}"
    asyncio.current_task().set_name(task_name)
    if config is None:
        config = SprintFetchConfig()

    # Register for monitoring
    global_async_process_tracker.register_process(task_name, "task", project_key)

    service = SprintDetailsService(config)
    try:
        app_container.schema.override(project_key)

        async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
            # Get board IDs
            board_ids = await service.get_board_ids(pg_async_session, project_key)

            if not board_ids:
                my_logger.warning(f"No scrum boards found for project {project_key}")
                return "+0"

            my_logger.debug(f"Found {len(board_ids)} boards for project {project_key}")
            total_sprints = 0
            timeout = aiohttp.ClientTimeout(total=300)
            connector = aiohttp.TCPConnector(
                limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
                ssl=ssl.create_default_context()
            )
            async with aiohttp.ClientSession(
                headers=jira_entry.custom_properties, timeout=timeout,
                connector=connector, raise_for_status=True
            ) as http_session:
                http_client = QueuedHttpClient(http_session, config)
                request_builder = JiraRequestBuilder(jira_entry, config)

                try:
                    for board_id in board_ids:
                        my_logger.debug(f"Processing board {board_id}")

                        # Fetch and process data for this board
                        df_final = await service.fetch_sprint_data_for_board(
                            http_client, request_builder, board_id, max_loop_count, my_logger
                        )

                        if not df_final.empty:
                            # Upsert to database
                            result = await service.db_manager.upsert_sprints(pg_async_session, df_final)
                            my_logger.debug(f"Board {board_id} result: {result}")

                            if result.startswith('+'):
                                total_sprints += int(result[1:])
                                # Update progress tracking
                                global_async_process_tracker.update_process(task_name, "running", total_sprints)
                                if progress_callback:
                                    progress_callback(50, f"Processing sprints: {total_sprints}")
                        else:
                            my_logger.warning(f"No data processed for board {board_id}")

                finally:
                    # Mark as completed
                    global_async_process_tracker.update_process(task_name, "completed", total_sprints)
                    if progress_callback:
                        progress_callback(100, f"Completed: {total_sprints} sprints")
                    await pg_async_session.commit()
                    await http_client.shutdown()

                    my_logger.debug(f"get_sprint_details completed successfully! Total sprints: {total_sprints}")

                return f"+{total_sprints}"
    except Exception as e:
        my_logger.error(f"Error in get_sprint_details: {str(e)}")
        global_async_process_tracker.update_process(task_name, "failed")
        handle_exception(e)
        return "FAILED"
    # ret_string = ""
    # try:
    #     app_container.schema.override(project_key)
    #     async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
    #         # with Database(schema=project_key).session() as pg_session:
    #         stmt = select(AllBoards.id).filter(
    #             (AllBoards.projectKey.in_([project_key.upper()])) & (AllBoards.type.in_(['scrum']))
    #         )
    #         result = await pg_async_session.execute(stmt)
    #         rows: list = result.scalars().all()
    #
    #         async with aiohttp.ClientSession(headers=jira_entry.custom_properties) as http_session:
    #             for board_id in rows:
    #                 url = f"{jira_entry.url}/rest/agile/1.0/board/{board_id}/sprint"
    #
    #                 tasks = []
    #                 for i in range(max_loop_count):
    #                     payload_dict = {'maxResults': 50, 'startAt': i * 50}
    #                     tasks.append(fetch_with_retries_get_new(http_session, url, payload_dict))
    #
    #                 url = f"{jira_entry.url}/rest/greenhopper/1.0/rapid/charts/velocity"
    #                 payload_dict = {'rapidViewId': board_id}
    #                 tasks.append(
    #                     fetch_with_retries_get_new(http_session, url, payload_dict)
    #                 )
    #
    #                 responses = await asyncio.gather(*tasks, return_exceptions=True)
    #                 my_logger.dataframe_utils("responses are:")
    #                 my_logger.dataframe_utils(f"{responses}")
    #
    #                 successful_responses = [resp["result"] for resp in responses if resp.get("success")]
    #                 exceptions = [resp["exception"] for resp in responses if not resp.get("success")]
    #
    #                 # my_logger.dataframe_utils(
    #                 # f"# of successful_responses: {[len(sublist) for sublist in successful_responses]}"
    #                 # )
    #                 # my_logger.dataframe_utils(f"# of exceptions: {len(exceptions)}")
    #
    #                 # for result in responses:
    #                 #     if isinstance(result, Exception):
    #                 #         my_logger.error(f"Task raised an exception: {result}")
    #                 my_logger.dataframe_utils(f"{successful_responses}")
    #
    #                 df = pd.DataFrame(
    #                     # [value for response in responses[:max_loop_count] for value in response["values"]]
    #                     [value for response in successful_responses[:max_loop_count] for value in response["values"]]
    #                 )
    #                 if 'self' in df.columns:
    #                     df.drop(columns=['self'], inplace=True)
    #
    #                 # velocity_stat_entries = responses[max_loop_count:][0]["velocityStatEntries"]
    #                 velocity_stat_entries = successful_responses[max_loop_count:][0]["velocityStatEntries"]
    #
    #                 df_velocity = pd.DataFrame.from_dict(velocity_stat_entries, orient="index").reset_index()
    #                 df_velocity['index'] = df_velocity['index'].astype('int64')
    #
    #                 df = df.merge(df_velocity, how='left', left_on='id', right_on='index')
    #                 with DataFrameDebugger() as debugger:
    #                     debug_dataframe = debugger.debug_dataframe
    #                     debug_dataframe(df, f"df.xlsx")
    #                     debug_dataframe(df_velocity, f"df_velocity.xlsx")
    #
    #                 tasks = []
    #                 semaphore = asyncio.Semaphore(15)
    #                 url = f"{jira_entry.url}/rest/greenhopper/1.0/rapid/charts/scopechangeburndownchart"
    #                 for sprint_id in df['id'].tolist():
    #                     payload_dict = {'rapidViewId': board_id, 'sprintId': sprint_id}
    #                     tasks.append(
    #                         fetch_sprints(
    #                             sprint_id, http_session, url,
    #                             payload=payload_dict, semaphore=semaphore
    #                         )
    #                     )
    #                 responses = await asyncio.gather(*tasks, return_exceptions=True)
    #
    #                 responses = [response for response in responses]
    #                 df_burndown = pd.json_normalize(responses)
    #
    #                 for col in ['changes', 'issueToParentKeys', 'issueToSummary', 'workRateData',
    #                             'openCloseChanges']:
    #                     # df_burndown[col] = df_burndown[col].apply(list_of_dicts_to_dict)
    #                     df_burndown[col] = df_burndown[col].map(lambda x: x[0] if x else {})
    #
    #                 df = df.merge(df_burndown, how='left', left_on='id', right_on='sprint_id')
    #                 df.drop(columns=['index', 'sprint_id'], inplace=True)
    #
    #                 # for col in ['startTime', 'endTime', 'completeTime', 'now']:
    #                 #     df[col] = df[col].astype(pd.Int64Dtype())
    #
    #                 df = cast_columns(df, ['startTime', 'endTime', 'completeTime', 'now'], pd.Int64Dtype())
    #                 df = cast_columns(df, ['startDate', 'endDate', 'completeDate', 'createdDate'], "datetime")
    #
    #                 # for col in ['startDate', 'endDate', 'completeDate', 'createdDate']:
    #                 #     # Safely convert to datetime
    #                 #     df[col] = pd.to_datetime(df[col], errors="coerce")
    #                 with DataFrameDebugger() as debugger:
    #                     debug_dataframe = debugger.debug_dataframe
    #                     debug_dataframe(df_burndown, "df_burndown.xlsx")
    #                     debug_dataframe(df, "df_final.xlsx")
    #
    #                 x = await upsert_async(
    #                     pg_async_session, Sprint, df, on_conflict_update=True
    #                 )
    #                 my_logger.dataframe_utils(f"return value = {x}")
    #                 ret_string = f"+{df.shape[0]}"
    #                 await pg_async_session.commit()
    #     my_logger.dataframe_utils("get_sprint_details completed!!!")
    # except Exception as e:
    #     ret_string = "FAILED"
    #     handle_exception(e)
    # finally:
    #     return ret_string


class IssueTypeClassification(Enum):
    initiative = 1
    epic = 2
    standard = 3
    subtask = 4


def prepare_issue_classification_data(pg_session) -> pd.DataFrame:
    topq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                            Issue.issue_hierarchy_level,
                            literal(1).label('level'),
                            case(

                                (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                (Issue.issuetype == 'Epic', 'epic'),
                                (Issue.isSubTask.is_(True), 'subtask'),

                                else_="standard"
                            ).label("issueclass"),
                            cast(cast(Issue.id, TEXT), LtreeType).label("path_id"),
                            cast(func.replace(Issue.key, "-", "_"), LtreeType).label("path_key")
                            )
    topq = topq.filter(Issue.parent_key.is_(None))
    topq = topq.cte('cte', recursive=True)

    bottomq = pg_session.query(Issue.id, Issue.key, Issue.issuetype, Issue.isSubTask, Issue.parent_key,
                               Issue.issue_hierarchy_level,
                               topq.c.level + 1,
                               case(
                                   (Issue.issuetype.in_(['Initiative ', 'Initiative']), 'initiative'),
                                   (Issue.issuetype == 'Epic', 'epic'),
                                   (Issue.isSubTask.is_(True), 'subtask'),
                                   else_="standard"
                               ).label("issueclass"),
                               topq.c.path_id.op('||')(func.text2ltree(cast(Issue.id, TEXT))),
                               topq.c.path_key.op('||')(func.text2ltree(func.replace(Issue.key, "-", "_")))
                               )
    bottomq = bottomq.join(topq, Issue.parent_key == topq.c.key)
    recursive_q = topq.union_all(bottomq)
    res = pg_session.query(recursive_q).all()
    df = pd.DataFrame(res)
    # df = pd.read_sql(session.query(recursive_q).statement, session.bind)
    return df


def chunked_iterable(iterable, size):
    """Yield chunks of `size` from `iterable`."""
    it = iter(iterable)
    for first in it:
        yield [first] + list(islice(it, size - 1))


@inject
async def upsert_issue_classification(
        project_key: str,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],
        progress_callback=None
):
    total_rows = 0
    task_name = f"upsert_issue_classification_{project_key}"
    asyncio.current_task().set_name(task_name)

    # Register for monitoring
    global_async_process_tracker.register_process(task_name, "task", project_key)

    try:
        app_container.schema.override(project_key)
        with app_container.database_rw().update_schema(project_key).session() as pg_session:
            df = prepare_issue_classification_data(pg_session)
            my_logger.debug(f"Total rows: {df.shape[0]}")
            total_rows = df.shape[0]

        # Copy id, key value to respective columns
        if df.empty:
            global_async_process_tracker.update_process(task_name, "completed", 0)
            return

        my_logger.debug(f"processing df")

        # Update progress tracking
        global_async_process_tracker.update_process(task_name, "running", total_rows)
        if progress_callback:
            progress_callback(25, f"Processing {total_rows} classifications")

        for issue_type in IssueTypeClassification:
            df[f'{issue_type.name}_id'] = df[df['issueclass'] == issue_type.name]['id']
            df[f'{issue_type.name}_key'] = df[df['issueclass'] == issue_type.name]['key']

        dict_initiative = df[df['issueclass'] == "initiative"][
            ['key', 'initiative_id', "initiative_key"]].set_index('key').T.to_dict('list')

        for count, field in enumerate(["initiative_id", "initiative_key"]):
            df.loc[df['issueclass'] == "epic", [field]] = \
                df['parent_key'].map(lambda x: dict_initiative.get(x, [None] * 2)[count])

        dict_epic = df[df['issueclass'] == "epic"][
            ['key', 'initiative_id', "initiative_key", "epic_id", "epic_key"]].set_index('key').T.to_dict('list')

        for count, field in enumerate(["initiative_id", "initiative_key", "epic_id", "epic_key", ]):
            df.loc[df['issueclass'] == "standard", [field]] = \
                df['parent_key'].map(lambda x: dict_epic.get(x, [None] * 4)[count])

        dict_standard = df[df['issueclass'] == "standard"][
            ['key', 'initiative_id', "initiative_key", "epic_id", "epic_key", "standard_id",
             "standard_key"]].set_index(
            'key').T.to_dict('list')

        for count, field in enumerate(
                ["initiative_id", "initiative_key", "epic_id", "epic_key", "standard_id", "standard_key"]
        ):
            df.loc[df['issueclass'] == "subtask", [field]] = \
                df['parent_key'].map(
                    lambda x: np.nan if dict_standard.get(x, [None] * 6)[count] is None else
                    dict_standard.get(x, [None] * 6)[count]
                )

        # Subtasks mapped to epic
        for count, field in enumerate(["initiative_id", "initiative_key", "epic_id", "epic_key"]):
            df.loc[(df['issueclass'] == "subtask") & (pd.isna(df["standard_id"])), [field]] = \
                df['parent_key'].map(lambda x: dict_epic.get(x, [None] * 4)[count])

        # Subtasks mapped to initiative
        for count, field in enumerate(["initiative_id", "initiative_key"]):
            df.loc[(df['issueclass'] == "subtask") & (pd.isna(df["epic_id"])), [field]] = \
                df['parent_key'].map(lambda x: dict_initiative.get(x, [None] * 2)[count])

        df.drop(columns=['isSubTask', 'parent_key', 'level', 'issuetype'], inplace=True)
        my_logger.debug(f"setting int types")
        # for col in ['initiative_id', 'epic_id', 'standard_id', 'subtask_id']:
        #     df[col] = df[col].astype(pd.Int64Dtype())
        df = cast_columns(df, ['initiative_id', 'epic_id', 'standard_id', 'subtask_id'], pd.Int64Dtype())
        # df[['initiative_id', 'epic_id', 'standard_id', 'subtask_id']] = df[
        #     ['initiative_id', 'epic_id', 'standard_id', 'subtask_id']].astype(pd.Int64Dtype())

        # df = df.astype({'initiative_id': int, 'epic_id': int, 'standard_id': int, 'subtask_id': int})

        # my_logger.dataframe_utils(f"Memory used: {humansize(sys.getsizeof(df))}")
        # my_logger.dataframe_utils(f"Memory used: {humansize(df.memory_usage(index=False, deep=True).sum())}")
        # my_logger.dataframe_utils(f"df information: {df.info()}")
        total_rows = df.shape[0]

        # Initialize the rich progress bar

        async with app_container.database_rw().update_schema(project_key).async_session() as pg_async_session:
            asyncio.current_task().set_name("upsert_issue_classification_db")
            conflict_condition = and_(
                or_(
                    and_(
                        getattr(IssueClassification, "initiative_id").is_(None),
                        getattr(insert(IssueClassification.__table__).excluded, "initiative_id").is_not(None),
                    ),
                    and_(
                        getattr(IssueClassification, "initiative_id").is_not(None),
                        getattr(insert(IssueClassification.__table__).excluded, "initiative_id").is_(None),
                    ),
                    getattr(IssueClassification, "initiative_id") !=
                    getattr(insert(IssueClassification.__table__).excluded, "initiative_id"),
                ),
                or_(
                    and_(
                        getattr(IssueClassification, "epic_id").is_(None),
                        getattr(insert(IssueClassification.__table__).excluded, "epic_id").is_not(None),
                    ),
                    and_(
                        getattr(IssueClassification, "epic_id").is_not(None),
                        getattr(insert(IssueClassification.__table__).excluded, "epic_id").is_(None),
                    ),
                    getattr(IssueClassification, "epic_id") !=
                    getattr(insert(IssueClassification.__table__).excluded, "epic_id"),
                ),
                or_(
                    and_(
                        getattr(IssueClassification, "standard_id").is_(None),
                        getattr(insert(IssueClassification.__table__).excluded, "standard_id").is_not(None),
                    ),
                    and_(
                        getattr(IssueClassification, "standard_id").is_not(None),
                        getattr(insert(IssueClassification.__table__).excluded, "standard_id").is_(None),
                    ),
                    getattr(IssueClassification, "standard_id") !=
                    getattr(insert(IssueClassification.__table__).excluded, "standard_id"),
                ),
                or_(
                    and_(
                        getattr(IssueClassification, "subtask_id").is_(None),
                        getattr(insert(IssueClassification.__table__).excluded, "subtask_id").is_not(None),
                    ),
                    and_(
                        getattr(IssueClassification, "subtask_id").is_not(None),
                        getattr(insert(IssueClassification.__table__).excluded, "subtask_id").is_(None),
                    ),
                    getattr(IssueClassification, "subtask_id") !=
                    getattr(insert(IssueClassification.__table__).excluded, "subtask_id"),
                ),
            )
            my_logger.info(f"conflict_condition type = {type(conflict_condition)}")
            my_logger.debug(f"{conflict_condition}")

            await upsert_async(pg_async_session, IssueClassification, df, conflict_condition=conflict_condition)
            # for i, start in enumerate(range(0, total_rows, batch_size)):
            #     batch_df = df.iloc[start:start + batch_size]
            #     await upsert_async(pg_async_session, IssueClassification, batch_df, )
            #     my_logger.dataframe_utils(f"batch {i} completed")
            await pg_async_session.commit()

            # Mark as completed
            global_async_process_tracker.update_process(task_name, "completed", total_rows)
            if progress_callback:
                progress_callback(100, f"Completed: {total_rows} classifications")

        # for batch_data in chunked_iterable(df.to_dict('records'), batch_size):
        #     # Convert the list of dictionaries back to a DataFrame
        #     batch_df = pd.DataFrame(batch_data)
        #
        #     upsert(pg_session, IssueClassification, batch_df, "id")
    except Exception as e:
        global_async_process_tracker.update_process(task_name, "failed")
        handle_exception(e)

    return f"+{total_rows}"


@inject
def create_refresh_mv(
        project_list: list,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    view_name = "run_details_view"
    my_logger.debug(f"executing create_refresh_mv. project list; {project_list}")
    project_names = ["acq", "plat"]

    app_container.schema.override('public')
    with app_container.database_rw().update_schema('public').session() as pg_session:
        # Construct the union query based on the list of project names
        union_queries = [
            f"SELECT '{project}' AS schema_name, topic, last_run FROM {project}.run_details_jira"
            for project in project_names
        ]
        union_query = " UNION ".join(union_queries)

        query = text(f"""
                CREATE MATERIALIZED VIEW IF NOT EXISTS {view_name} AS
                {union_query}
                ORDER BY schema_name
                """)
        pg_session.execute(query)
        pg_session.commit()

        pg_session.execute(text(f"REFRESH MATERIALIZED VIEW {view_name}"))
        pg_session.commit()
    my_logger.debug("create_refresh_mv completed!!!")


def format_time_difference(total_time_ns: int):
    # Convert nanoseconds to timedelta
    diff_s = total_time_ns / 1_000_000_000
    time_diff = timedelta(seconds=diff_s)

    # Extract hours, minutes, seconds, and milliseconds
    total_seconds = int(time_diff.total_seconds())
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    milliseconds = int(time_diff.microseconds / 1000)

    # Format the result as hh:mm:ss.ms
    return f"{hours:02}:{minutes:02}:{seconds:02}.{milliseconds:03}"


# Rate limit constants
MAX_RETRIES = 5
INITIAL_RETRY_DELAY = 5000.0  # in milliseconds
MAX_RETRY_DELAY = 10000  # in milliseconds
JITTER_MULTIPLIER_RANGE = (0.5, 1.5)
JIRA_BASE_URL = 'https://corecard.atlassian.net'


async def fetch_sprints(
        sprint_id,
        session: aiohttp.ClientSession, url, payload: dict,
        semaphore: asyncio.locks.Semaphore,
        my_logger: Logger = None,
):
    try:
        async with semaphore:
            responses = await fetch_with_retries_get(session, url, payload)
            if responses.get("success"):
                response = responses.get("result")
                response['sprint_id'] = sprint_id
                response.pop('statisticField', None)
                if 'lastUserWhoClosedHtml' in response:
                    html_content = response['lastUserWhoClosedHtml']
                    soup = BeautifulSoup(html_content, 'html.parser')
                    a_tag = soup.find('a')
                    response['lastUserWhoClosedHtml'] = a_tag.get_text()
                for key, value in response.get("openCloseChanges", {}).items():
                    for item in value:
                        html_content = item.get("userDisplayNameHtml", "")
                        soup = BeautifulSoup(html_content, 'html.parser')
                        item["userDisplayNameHtml"] = soup.find('a').get_text() if soup.find('a') else html_content
                for key, value in response.items():
                    if isinstance(value, dict):
                        response[key] = [value]
                return response
            else:
                return responses.get("exception")
    except Exception as e:
        if my_logger:
            my_logger.error(f"Exception in fetch_sprints for {sprint_id}: {e}", exc_info=True)
        return {"success": False, "exception": str(e)}


# Function to add new key-value pairs with parameters from DataFrame

def add_issue_key_issue_id(d, issue_key, issue_id):
    try:
        d['issue_key'] = issue_key
        d['issue_id'] = issue_id
        return d
    except Exception as e:
        raise e

def remove_empty_attrs(obj):
    if isinstance(obj, dict):
        obj_type = obj.get("type")
        new_obj = {}
        for k, v in obj.items():
            if k == "attrs" and isinstance(v, dict) and not v and obj_type in ["tableCell", "tableHeader"]:
                continue  # Skip empty 'attrs' only if parent is tableCell
            new_obj[k] = remove_empty_attrs(v)
        return new_obj
    elif isinstance(obj, list):
        return [remove_empty_attrs(item) for item in obj]
    return obj

def remove_nested_empty_dicts_and_empty_adf(obj):
    def _clean(o, is_nested=False):
        if isinstance(o, dict):
            # Special case: Empty ADF doc node
            if (
                o.get("type") == "doc"
                and isinstance(o.get("content"), list)
                and len(o["content"]) == 0
            ):
                print("Empty ADF doc node found")
                return None

            new_obj = {}
            for k, v in o.items():
                cleaned = _clean(v, is_nested=True)
                if isinstance(cleaned, dict) and not cleaned:
                    continue
                new_obj[k] = cleaned
            return new_obj if new_obj or not is_nested else {}

        elif isinstance(o, list):
            return [_clean(item, is_nested=True) for item in o if item != {}]

        return o

    return _clean(obj, is_nested=False)



# OPTION 1: STREAMING
class HierarchyAwareProcessor:
    """Memory-efficient processor that leverages issue_hierarchy_level for optimal ordering"""

    def __init__(self, db_rw, logger, get_model_by_name_func, chunk_size: int = 1000):
        self.db_rw = db_rw
        self.logger = logger
        self.get_model_by_name = get_model_by_name_func
        self.chunk_size = chunk_size
        self.temp_dir = Path(tempfile.mkdtemp(prefix="hierarchy_processor_"))

        # Separate storage for Issues vs child tables
        self.issue_files = []  # Store Issue records
        self.child_table_files = defaultdict(list)  # Store child table records

        # Define processing order based on hierarchy and dependencies
        self.issue_child_tables = {
            'WorkLog', 'ChangelogJSON', 'IssueComments', 'IssueLinks'
        }

    async def add_record(self, model_name: str, record_data: Dict[str, Any]):
        await asyncio.to_thread(self._add_record_sync, model_name, record_data)

    def _add_record_sync(self, model_name: str, record_data: Dict[str, Any]):
        """Add record with hierarchy-aware categorization"""
        df = record_data['df']
        temp_file = self.temp_dir / f"{model_name}_{len(self.issue_files if model_name == 'Issue' else self.child_table_files[model_name])}.parquet"

        # Save DataFrame to disk immediately
        # empty_list_columns = []

        # Change empty list and dict to None later.
        # for col in df.columns:
        #     if df[col].apply(lambda x: isinstance(x, dict) and len(x) == 0).any():
        #         df[col] = df[col].apply(lambda x: None if isinstance(x, dict) and len(x) == 0 else x)
        #     # Check for empty lists
        #     empty_list_mask = df[col].apply(lambda x: isinstance(x, list) and len(x) == 0)
        #     if empty_list_mask.any():
        #         empty_list_columns.append(col)
        #         df[col] = df[col].apply(lambda x: None if isinstance(x, list) and len(x) == 0 else x)
        # This causes break
        # "content": [{"type": "tableCell", "attrs": {}, ...

        for col in ["description", "comment"]:
            if col in df.columns:
                # df[col] = df[col].apply(remove_empty_attrs)
                df[col] = df[col].apply(remove_nested_empty_dicts_and_empty_adf)

        try:
            df.to_parquet(temp_file, compression='snappy')
        except (ArrowNotImplementedError, ArrowTypeError) as arrow_error:
            print(f"Error saving DataFrame to {temp_file}: {arrow_error}")
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            df.to_csv(f"c:/vishal/log/failed_df_{timestamp}.csv", index=False)
            # Log all rows where any column is an empty dict
            table = pa.Table.from_pandas(df)
            print(table.schema)
        except Exception:
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
            print(f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}")


        file_record = {
            'file_path': temp_file,
            'model': record_data['model'],
            'model_name': model_name,
            'params': record_data['params'],
            'row_count': len(df)
        }

        # Categorize by table type
        if model_name == 'Issue':
            self.issue_files.append(file_record)
        elif model_name in self.issue_child_tables:
            self.child_table_files[model_name].append(file_record)
        else:
            # Handle other models (non-Issue related)
            self.child_table_files[model_name].append(file_record)

        # Immediate cleanup
        del df, record_data
        gc.collect()

    def _read_dataframe_chunks(self, file_path: Path) -> Generator[pd.DataFrame, None, None]:
        """Read DataFrame in chunks to minimize memory usage"""
        df = pd.read_parquet(file_path)

        for i in range(0, len(df), self.chunk_size):
            yield df.iloc[i:i + self.chunk_size].copy()

    async def _process_issues_by_hierarchy(self) -> Dict[str, Any]:
        """Process Issues using hierarchy_level for optimal ordering"""
        if not self.issue_files:
            return {'processed': 0, 'failed': 0}

        self.logger.info(f"Processing {len(self.issue_files)} Issue file groups")

        # Step 1: Collect all hierarchy levels and build level-based batches
        hierarchy_batches = defaultdict(list)  # level -> list of (file_record, chunk_df)

        for file_record in self.issue_files:
            for chunk in self._read_dataframe_chunks(file_record['file_path']):
                # Group by hierarchy level
                if 'issue_hierarchy_level' in chunk.columns:
                    for level in chunk['issue_hierarchy_level'].unique():
                        if pd.notna(level):
                            level_chunk = chunk[chunk['issue_hierarchy_level'] == level].copy()
                            if len(level_chunk) > 0:
                                hierarchy_batches[int(level)].append((file_record, level_chunk))
                else:
                    # Fallback: treat as level 0 if hierarchy_level is missing
                    hierarchy_batches[0].append((file_record, chunk))

                del chunk
                gc.collect()

        # Step 2: Process in hierarchy order (highest level first, then down to subtasks)
        total_processed = 0
        total_failed = 0

        # Sort levels: Process from highest hierarchy level down to -1 (subtasks)
        sorted_levels = sorted(hierarchy_batches.keys(), reverse=True)

        self.logger.info(f"Processing Issues across {len(sorted_levels)} hierarchy levels: {sorted_levels}")

        async with self.db_rw.async_session() as session:
            for level in sorted_levels:
                self.logger.info(f"Processing hierarchy level {level} with {len(hierarchy_batches[level])} batches")

                for file_record, level_chunk in hierarchy_batches[level]:
                    # Within same hierarchy level, we can still have parent-child relationships
                    # Sort by parent_id (nulls first) for additional safety
                    if 'parent_id' in level_chunk.columns:
                        level_chunk = level_chunk.sort_values(
                            by='parent_id',
                            na_position='first'
                        ).copy()

                    # Process in smaller sub-chunks if needed
                    for i in range(0, len(level_chunk), self.chunk_size):
                        sub_chunk = level_chunk.iloc[i:i + self.chunk_size].copy()

                        async with session.begin_nested() as savepoint:
                            try:
                                success = await upsert_async(
                                    session,
                                    file_record['model'],
                                    sub_chunk,
                                    **{k: v for k, v in file_record['params'].items()
                                       if k not in ['message_count', 'my_logger']}
                                )

                                if success:
                                    total_processed += len(sub_chunk)
                                    self.logger.debug(f"Level {level} sub-batch successful: {len(sub_chunk)} records")
                                else:
                                    await savepoint.rollback()
                                    total_failed += len(sub_chunk)
                                    await self._save_failed_batch(
                                        {'df': sub_chunk, 'model': file_record['model']},
                                        f"failed_level_{level}"
                                    )

                            except Exception as e:
                                await savepoint.rollback()
                                total_failed += len(sub_chunk)
                                self.logger.error(f"Error in level {level} batch: {e}")
                                await self._save_failed_batch(
                                    {'df': sub_chunk, 'model': file_record['model']},
                                    f"error_level_{level}"
                                )

                        del sub_chunk
                        gc.collect()

                    del level_chunk
                    gc.collect()

        return {'processed': total_processed, 'failed': total_failed}

    async def _process_child_tables(self) -> Dict[str, Any]:
        """Process child tables after Issues are complete"""
        total_processed = 0
        total_failed = 0
        model_results = {}

        # Define processing order for child tables (if there are dependencies between them)
        child_table_order = ['WorkLog', 'ChangelogJSON', 'IssueComments', 'IssueLinks']

        # Add any other child tables that might exist
        all_child_tables = set(self.child_table_files.keys())
        for table in all_child_tables:
            if table not in child_table_order:
                child_table_order.append(table)

        for model_name in child_table_order:
            if model_name not in self.child_table_files:
                continue

            self.logger.info(f"Processing child table: {model_name}")
            model_processed = 0
            model_failed = 0

            async with self.db_rw.async_session() as session:
                for file_record in self.child_table_files[model_name]:
                    for chunk in self._read_dataframe_chunks(file_record['file_path']):
                        # Process in sub-chunks
                        for i in range(0, len(chunk), self.chunk_size):
                            sub_chunk = chunk.iloc[i:i + self.chunk_size].copy()

                            async with session.begin_nested() as savepoint:
                                try:
                                    success = await upsert_async(
                                        session,
                                        file_record['model'],
                                        sub_chunk,
                                        **{k: v for k, v in file_record['params'].items()
                                           if k not in ['message_count', 'my_logger']}
                                    )

                                    if success:
                                        model_processed += len(sub_chunk)
                                    else:
                                        await savepoint.rollback()
                                        model_failed += len(sub_chunk)

                                except Exception as e:
                                    await savepoint.rollback()
                                    model_failed += len(sub_chunk)
                                    self.logger.error(f"Error in {model_name} batch: {e}")

                            del sub_chunk
                            gc.collect()

                        del chunk
                        gc.collect()

            model_results[model_name] = {'processed': model_processed, 'failed': model_failed}
            total_processed += model_processed
            total_failed += model_failed

            self.logger.info(f"Completed {model_name}: {model_processed} processed, {model_failed} failed")

        return {
            'processed': total_processed,
            'failed': total_failed,
            'model_results': model_results
        }

    async def process_all_models(self) -> Dict[str, Any]:
        """Process all models with hierarchy-aware ordering"""
        results = {
            'model_results': {},
            'total_processed': 0,
            'total_failed': 0
        }

        # Phase 1: Process Issues first (in hierarchy order)
        self.logger.info("Phase 1: Processing Issues by hierarchy level")
        issue_results = await self._process_issues_by_hierarchy()
        results['model_results']['Issue'] = issue_results
        results['total_processed'] += issue_results['processed']
        results['total_failed'] += issue_results['failed']

        # Phase 2: Process child tables
        self.logger.info("Phase 2: Processing child tables")
        child_results = await self._process_child_tables()
        results['model_results'].update(child_results['model_results'])
        results['total_processed'] += child_results['processed']
        results['total_failed'] += child_results['failed']

        # Cleanup temporary files
        self._cleanup_temp_files()

        return results

    async def _save_failed_batch(self, batch_record: Dict[str, Any], filename_prefix: str):
        """Save failed batch for later analysis"""
        try:
            df = batch_record['df']
            model_name = batch_record['model'].__name__
            filename = f"{filename_prefix}_{model_name}.xlsx"
            await quick_save_async(df, filename, path="c:/vishal/log/failed_hierarchy")
            self.logger.info(f"Saved failed batch to {filename}")
        except Exception as e:
            self.logger.error(f"Failed to save failed batch: {e}")

    def _cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            self.logger.error(f"Error cleaning up temp files: {e}")

# OPTION 2: POSTGRESQL TEMPORARY TABLES (Recommended)
class PostgreSQLHierarchyProcessor:
    """Memory-efficient processor using PostgreSQL temporary tables for staging"""

    def __init__(self, db_rw, logger, get_model_by_name_func, chunk_size: int = 1000):
        self.db_rw = db_rw
        self.logger = logger
        self.get_model_by_name = get_model_by_name_func
        self.chunk_size = chunk_size
        self.session_id = str(uuid.uuid4())[:8]  # Unique identifier for this processing session

        # Track staging table names for cleanup
        self.staging_tables = []

        self.issue_child_tables = {
            'WorkLog', 'ChangelogJSON', 'IssueComments', 'IssueLinks'
        }

    async def _setup_staging_tables(self, session):
        """Create PostgreSQL temporary tables for staging"""

        # Create temporary table for Issues
        await session.execute(text(f"""
            CREATE TEMPORARY TABLE temp_staged_issues_{self.session_id} (
                staging_id SERIAL PRIMARY KEY,
                issue_id BIGINT,
                issue_key TEXT,
                parent_id BIGINT,
                parent_key TEXT,
                hierarchy_level INTEGER,
                row_data JSONB,
                params JSONB,
                processed BOOLEAN DEFAULT FALSE,
                batch_id INTEGER DEFAULT 0
            ) ON COMMIT PRESERVE ROWS
        """))

        # Create indexes on temporary table
        await session.execute(text(f"""
            CREATE INDEX ON temp_staged_issues_{self.session_id} (hierarchy_level);
            CREATE INDEX ON temp_staged_issues_{self.session_id} (parent_id, parent_key);
            CREATE INDEX ON temp_staged_issues_{self.session_id} (issue_id, issue_key);
            CREATE INDEX ON temp_staged_issues_{self.session_id} (processed);
            CREATE INDEX ON temp_staged_issues_{self.session_id} (batch_id);
        """))

        # Create temporary table for child tables
        await session.execute(text(f"""
            CREATE TEMPORARY TABLE temp_staged_child_tables_{self.session_id} (
                staging_id SERIAL PRIMARY KEY,
                model_name TEXT,
                issue_id BIGINT,
                issue_key TEXT,
                row_data JSONB,
                params JSONB,
                processed BOOLEAN DEFAULT FALSE,
                batch_id INTEGER DEFAULT 0
            ) ON COMMIT PRESERVE ROWS
        """))

        # Create indexes on child table
        await session.execute(text(f"""
            CREATE INDEX ON temp_staged_child_tables_{self.session_id} (model_name);
            CREATE INDEX ON temp_staged_child_tables_{self.session_id} (issue_id, issue_key);
            CREATE INDEX ON temp_staged_child_tables_{self.session_id} (processed);
            CREATE INDEX ON temp_staged_child_tables_{self.session_id} (batch_id);
        """))

        self.staging_tables.extend([
            f'temp_staged_issues_{self.session_id}',
            f'temp_staged_child_tables_{self.session_id}'
        ])

        self.logger.info(f"Created PostgreSQL temporary staging tables: temp_staged_issues_{self.session_id} & temp_staged_child_tables_{self.session_id}")

    async def add_record(self, model_name: str, record_data: Dict[str, Any], session=None):
        """Add records to PostgreSQL staging tables"""
        if session is None:
            async with self.db_rw.async_session() as session:
                return await self._add_record_internal(model_name, record_data, session)
        else:
            return await self._add_record_internal(model_name, record_data, session)

    async def _add_record_internal(self, model_name: str, record_data: Dict[str, Any], session):
        """Internal method to add records with existing session"""
        df = record_data['df']
        params_json = json.dumps({k: v for k, v in record_data['params'].items()
                                  if k not in ['message_count', 'my_logger']})

        if model_name == 'Issue':
            # Insert Issues into staging table
            for _, row in df.iterrows():
                row_dict = row.to_dict()

                # Convert pandas types to JSON-serializable types
                for key, value in row_dict.items():
                    # Check if value is a scalar (not an array)
                    if pd.api.types.is_scalar(value):
                        if pd.isna(value):
                            row_dict[key] = None
                        elif hasattr(value, 'isoformat'):  # datetime objects
                            row_dict[key] = value.isoformat()
                        elif isinstance(value, (pd.Timestamp, pd.NaT.__class__)):
                            row_dict[key] = value.isoformat() if not pd.isna(value) else None
                    else:
                        # Handle non-scalar values (arrays, lists, etc.)
                        if hasattr(value, 'tolist'):
                            row_dict[key] = value.tolist()
                        elif hasattr(value, '__len__') and len(value) == 0:
                            row_dict[key] = None
                        else:
                            row_dict[key] = str(value)

                row_json = json.dumps(row_dict)

                await session.execute(text(f"""
                    INSERT INTO temp_staged_issues_{self.session_id} 
                    (issue_id, issue_key, parent_id, parent_key, hierarchy_level, row_data, params)
                    VALUES (:issue_id, :issue_key, :parent_id, :parent_key, :hierarchy_level, :row_data, :params)
                """), {
                    'issue_id': row.get('id'),
                    'issue_key': row.get('key'),
                    'parent_id': row.get('parent_id'),
                    'parent_key': row.get('parent_key'),
                    'hierarchy_level': row.get('issue_hierarchy_level'),
                    'row_data': row_json,
                    'params': params_json
                })

        elif model_name in self.issue_child_tables or model_name not in self.issue_child_tables:
            # Insert child tables into staging table
            for _, row in df.iterrows():
                row_dict = row.to_dict()

                # Convert pandas types to JSON-serializable types
                for key, value in row_dict.items():
                    if pd.isna(value):
                        row_dict[key] = None
                    elif hasattr(value, 'isoformat'):  # datetime objects
                        row_dict[key] = value.isoformat()
                    elif isinstance(value, (pd.Timestamp, pd.NaT.__class__)):
                        row_dict[key] = value.isoformat() if not pd.isna(value) else None

                row_json = json.dumps(row_dict)

                stmt = text(f"""
                    INSERT INTO temp_staged_child_tables_{self.session_id} 
                    (model_name, issue_id, issue_key, row_data, params)
                    VALUES (:model_name, :issue_id, :issue_key, :row_data, :params)
                """).bindparams(
                    bindparam('row_data', type_=JSON),
                    bindparam('params', type_=JSON)
                )

                await session.execute(stmt, {
                    'model_name': model_name,
                    'issue_id': row.get('issue_id', row.get('id')),
                    'issue_key': row.get('issue_key', row.get('key')),
                    'row_data': row_json,
                    'params': params_json
                })

        # Commit the batch
        await session.commit()

        # Clear DataFrame from memory immediately
        del df
        gc.collect()

        self.logger.debug(f"Staged {len(record_data['df'])} records for {model_name}")

    async def _process_issues_by_hierarchy(self, session) -> Dict[str, Any]:
        """Process Issues using PostgreSQL with hierarchy level ordering"""

        # Get hierarchy level statistics
        result = await session.execute(text(f"""
            SELECT 
                hierarchy_level,
                COUNT(*) as count,
                MIN(staging_id) as min_id,
                MAX(staging_id) as max_id
            FROM temp_staged_issues_{self.session_id}
            WHERE hierarchy_level IS NOT NULL
            GROUP BY hierarchy_level
            ORDER BY hierarchy_level DESC
        """))

        hierarchy_stats = result.fetchall()
        self.logger.info(f"Issue hierarchy distribution: {[(row[0], row[1]) for row in hierarchy_stats]}")

        if not hierarchy_stats:
            self.logger.warning("No Issues with hierarchy_level found")
            return {'processed': 0, 'failed': 0}

        total_processed = 0
        total_failed = 0

        # Process each hierarchy level from highest to lowest
        for level, count, min_id, max_id in hierarchy_stats:
            self.logger.info(f"Processing hierarchy level {level} ({count} records)")

            # Process this level in batches
            batch_id = 0
            offset = 0

            while True:
                batch_id += 1

                # Get batch for this hierarchy level with additional ordering for consistency
                result = await session.execute(text(f"""
                    SELECT staging_id, row_data, params
                    FROM temp_staged_issues_{self.session_id}
                    WHERE hierarchy_level = :level 
                    AND processed = FALSE
                    ORDER BY 
                        CASE WHEN parent_id IS NULL THEN 0 ELSE 1 END,
                        parent_id NULLS FIRST,
                        staging_id
                    LIMIT :limit OFFSET :offset
                """), {
                    'level': level,
                    'limit': self.chunk_size,
                    'offset': offset
                })

                batch_rows = result.fetchall()
                if not batch_rows:
                    break

                # Convert JSONB data back to DataFrame
                batch_data = []
                params = None
                staging_ids = []

                for staging_id, row_data_json, params_json in batch_rows:
                    row_dict = dict(row_data_json)  # JSONB automatically converts to dict

                    # Convert ISO date strings back to datetime objects if needed
                    for key, value in row_dict.items():
                        if isinstance(value, str) and 'T' in value and (
                                'Z' in value or '+' in value or value.endswith(':00')):
                            try:
                                row_dict[key] = pd.to_datetime(value)
                            except:
                                pass  # Keep as string if conversion fails

                    batch_data.append(row_dict)
                    staging_ids.append(staging_id)

                    if params is None:
                        params = dict(params_json)  # JSONB automatically converts to dict

                if batch_data:
                    batch_df = pd.DataFrame(batch_data)

                    # Use nested savepoint for batch processing
                    savepoint = await session.begin_nested()

                    try:
                        model = self.get_model_by_name('Issue')
                        success = await upsert_async(
                            session,
                            model,
                            batch_df,
                            **params
                        )

                        if success:
                            await savepoint.commit()
                            total_processed += len(batch_df)

                            # Mark batch as processed
                            await session.execute(text(f"""
                                UPDATE temp_staged_issues_{self.session_id}
                                SET processed = TRUE, batch_id = :batch_id
                                WHERE staging_id = ANY(:staging_ids)
                            """), {
                                'batch_id': batch_id,
                                'staging_ids': staging_ids
                            })

                            self.logger.debug(f"Level {level} batch {batch_id} successful: {len(batch_df)} records")
                        else:
                            await savepoint.rollback()
                            total_failed += len(batch_df)
                            await self._save_failed_batch(batch_df, f"failed_issue_level_{level}_batch_{batch_id}")

                    except Exception as e:
                        await savepoint.rollback()
                        total_failed += len(batch_df)
                        self.logger.error(f"Error in Issue level {level} batch {batch_id}: {e}")
                        await self._save_failed_batch(batch_df, f"error_issue_level_{level}_batch_{batch_id}")

                    del batch_df, batch_data
                    gc.collect()

                offset += self.chunk_size

            # Commit after each level
            await session.commit()
            self.logger.info(f"Completed hierarchy level {level}")

        return {'processed': total_processed, 'failed': total_failed}

    async def _process_child_tables(self, session) -> Dict[str, Any]:
        """Process child tables using PostgreSQL staging"""

        # Get child table statistics
        result = await session.execute(text(f"""
            SELECT model_name, COUNT(*) as count
            FROM temp_staged_child_tables_{self.session_id}
            GROUP BY model_name
            ORDER BY model_name
        """))

        child_stats = result.fetchall()
        self.logger.info(f"Child table distribution: {[(row[0], row[1]) for row in child_stats]}")

        total_processed = 0
        total_failed = 0
        model_results = {}

        # Define processing order for child tables
        child_table_order = ['WorkLog', 'ChangelogJSON', 'IssueComments', 'IssueLinks']

        # Add any other child tables that exist in the data
        existing_tables = {row[0] for row in child_stats}
        for table in existing_tables:
            if table not in child_table_order:
                child_table_order.append(table)

        for model_name in child_table_order:
            if model_name not in existing_tables:
                continue

            self.logger.info(f"Processing child table: {model_name}")
            model_processed = 0
            model_failed = 0
            batch_id = 0
            offset = 0

            while True:
                batch_id += 1

                # Get batch for this model
                result = await session.execute(text(f"""
                    SELECT staging_id, row_data, params
                    FROM temp_staged_child_tables_{self.session_id}
                    WHERE model_name = :model_name 
                    AND processed = FALSE
                    ORDER BY staging_id
                    LIMIT :limit OFFSET :offset
                """), {
                    'model_name': model_name,
                    'limit': self.chunk_size,
                    'offset': offset
                })

                batch_rows = result.fetchall()
                if not batch_rows:
                    break

                # Convert JSONB data back to DataFrame
                batch_data = []
                params = None
                staging_ids = []

                for staging_id, row_data_json, params_json in batch_rows:
                    row_dict = dict(row_data_json)

                    # Convert ISO date strings back to datetime objects if needed
                    for key, value in row_dict.items():
                        if isinstance(value, str) and 'T' in value and (
                                'Z' in value or '+' in value or value.endswith(':00')):
                            try:
                                row_dict[key] = pd.to_datetime(value)
                            except:
                                pass

                    batch_data.append(row_dict)
                    staging_ids.append(staging_id)

                    if params is None:
                        params = dict(params_json)

                if batch_data:
                    batch_df = pd.DataFrame(batch_data)

                    savepoint = await session.begin_nested()

                    try:
                        model = self.get_model_by_name(model_name)
                        success = await upsert_async(
                            session,
                            model,
                            batch_df,
                            **params
                        )

                        if success:
                            await savepoint.commit()
                            model_processed += len(batch_df)

                            # Mark batch as processed
                            await session.execute(text(f"""
                                UPDATE temp_staged_child_tables_{self.session_id}
                                SET processed = TRUE, batch_id = :batch_id
                                WHERE staging_id = ANY(:staging_ids)
                            """), {
                                'batch_id': batch_id,
                                'staging_ids': staging_ids
                            })

                        else:
                            await savepoint.rollback()
                            model_failed += len(batch_df)

                    except Exception as e:
                        await savepoint.rollback()
                        model_failed += len(batch_df)
                        self.logger.error(f"Error in {model_name} batch {batch_id}: {e}")

                    del batch_df, batch_data
                    gc.collect()

                offset += self.chunk_size

            model_results[model_name] = {'processed': model_processed, 'failed': model_failed}
            total_processed += model_processed
            total_failed += model_failed

            # Commit after each model
            await session.commit()
            self.logger.info(f"Completed {model_name}: {model_processed} processed, {model_failed} failed")

        return {
            'processed': total_processed,
            'failed': total_failed,
            'model_results': model_results
        }

    async def process_all_models(self) -> Dict[str, Any]:
        """Process all models with PostgreSQL staging"""
        results = {
            'model_results': {},
            'total_processed': 0,
            'total_failed': 0
        }

        # Use a single long-running session for the entire processing
        async with self.db_rw.async_session() as session:
            # Setup staging tables
            await self._setup_staging_tables(session)

            try:
                # Phase 1: Process Issues by hierarchy
                self.logger.info("Phase 1: Processing Issues by hierarchy level")
                issue_results = await self._process_issues_by_hierarchy(session)
                results['model_results']['Issue'] = issue_results
                results['total_processed'] += issue_results['processed']
                results['total_failed'] += issue_results['failed']

                # Phase 2: Process child tables
                self.logger.info("Phase 2: Processing child tables")
                child_results = await self._process_child_tables(session)
                results['model_results'].update(child_results['model_results'])
                results['total_processed'] += child_results['processed']
                results['total_failed'] += child_results['failed']

                # Final commit
                await session.commit()

            except Exception as e:
                await session.rollback()
                self.logger.error(f"Error in processing, rolling back: {e}")
                raise
            finally:
                # Cleanup happens automatically when session ends (temporary tables are dropped)
                pass

        return results

    async def _save_failed_batch(self, batch_df: pd.DataFrame, filename_prefix: str):
        """Save failed batch for later analysis"""
        try:
            filename = f"{filename_prefix}.xlsx"
            await quick_save_async(batch_df, filename, path="c:/vishal/log/failed_postgres")
            self.logger.info(f"Saved failed batch to {filename}")
        except Exception as e:
            self.logger.error(f"Failed to save failed batch: {e}")


# OPTION 3: POSTGRESQL PERSISTENT STAGING SCHEMA (For very large datasets)
class PostgreSQLPersistentStagingProcessor:
    """Use a persistent staging schema in PostgreSQL for very large datasets"""

    def __init__(self, db_rw, logger, get_model_by_name_func, staging_schema: str = "staging_temp",
                 chunk_size: int = 1000):
        self.db_rw = db_rw
        self.logger = logger
        self.get_model_by_name = get_model_by_name_func
        self.staging_schema = staging_schema
        self.session_id = str(uuid.uuid4())[:8]
        self.chunk_size = chunk_size

        self.issue_child_tables = {
            'WorkLog', 'ChangelogJSON', 'IssueComments', 'IssueLinks'
        }

    async def _setup_staging_schema(self, session):
        """Create staging schema and tables"""

        # Create schema if it doesn't exist
        await session.execute(text(f"CREATE SCHEMA IF NOT EXISTS {self.staging_schema}"))

        # Create staging tables in the schema
        await session.execute(text(f"""
            CREATE TABLE IF NOT EXISTS {self.staging_schema}.staged_issues_{self.session_id} (
                staging_id BIGSERIAL PRIMARY KEY,
                issue_id BIGINT,
                issue_key TEXT,
                parent_id BIGINT,
                parent_key TEXT,
                hierarchy_level INTEGER,
                row_data JSONB,
                params JSONB,
                processed BOOLEAN DEFAULT FALSE,
                batch_id INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """))

        await session.execute(text(f"""
            CREATE TABLE IF NOT EXISTS {self.staging_schema}.staged_child_tables_{self.session_id} (
                staging_id BIGSERIAL PRIMARY KEY,
                model_name TEXT,
                issue_id BIGINT,
                issue_key TEXT,
                row_data JSONB,
                params JSONB,
                processed BOOLEAN DEFAULT FALSE,
                batch_id INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """))

        # Create indexes
        await session.execute(text(f"""
            CREATE INDEX IF NOT EXISTS idx_staged_issues_{self.session_id}_hierarchy 
            ON {self.staging_schema}.staged_issues_{self.session_id} (hierarchy_level);

            CREATE INDEX IF NOT EXISTS idx_staged_issues_{self.session_id}_processed 
            ON {self.staging_schema}.staged_issues_{self.session_id} (processed);

            CREATE INDEX IF NOT EXISTS idx_staged_child_{self.session_id}_model 
            ON {self.staging_schema}.staged_child_tables_{self.session_id} (model_name);

            CREATE INDEX IF NOT EXISTS idx_staged_child_{self.session_id}_processed 
            ON {self.staging_schema}.staged_child_tables_{self.session_id} (processed);
        """))

        await session.commit()
        self.logger.info(f"Created staging schema {self.staging_schema} with session {self.session_id}")

    async def add_record(self, model_name: str, record_data: Dict[str, Any], session=None):
        """Add records to PostgreSQL persistent staging tables"""
        if session is None:
            async with self.db_rw.async_session() as session:
                return await self._add_record_internal(model_name, record_data, session)
        else:
            return await self._add_record_internal(model_name, record_data, session)

    async def _add_record_internal(self, model_name: str, record_data: Dict[str, Any], session):
        """Internal method to add records with existing session"""
        df = record_data['df']
        params_json = json.dumps({k: v for k, v in record_data['params'].items()
                                  if k not in ['message_count', 'my_logger']})

        if model_name == 'Issue':
            # Insert Issues into persistent staging table
            for _, row in df.iterrows():
                row_dict = row.to_dict()

                # Convert pandas types to JSON-serializable types
                for key, value in row_dict.items():
                    if pd.isna(value):
                        row_dict[key] = None
                    elif hasattr(value, 'isoformat'):  # datetime objects
                        row_dict[key] = value.isoformat()
                    elif isinstance(value, (pd.Timestamp, pd.NaT.__class__)):
                        row_dict[key] = value.isoformat() if not pd.isna(value) else None

                row_json = json.dumps(row_dict)

                await session.execute(text(f"""
                    INSERT INTO {self.staging_schema}.staged_issues_{self.session_id} 
                    (issue_id, issue_key, parent_id, parent_key, hierarchy_level, row_data, params)
                    VALUES (:issue_id, :issue_key, :parent_id, :parent_key, :hierarchy_level, :row_data, :params)
                """), {
                    'issue_id': row.get('id'),
                    'issue_key': row.get('key'),
                    'parent_id': row.get('parent_id'),
                    'parent_key': row.get('parent_key'),
                    'hierarchy_level': row.get('issue_hierarchy_level'),
                    'row_data': row_json,
                    'params': params_json
                })

        elif model_name in self.issue_child_tables or model_name not in self.issue_child_tables:
            # Insert child tables into persistent staging table
            for _, row in df.iterrows():
                row_dict = row.to_dict()

                # Convert pandas types to JSON-serializable types
                for key, value in row_dict.items():
                    if pd.isna(value):
                        row_dict[key] = None
                    elif hasattr(value, 'isoformat'):  # datetime objects
                        row_dict[key] = value.isoformat()
                    elif isinstance(value, (pd.Timestamp, pd.NaT.__class__)):
                        row_dict[key] = value.isoformat() if not pd.isna(value) else None

                row_json = json.dumps(row_dict)

                await session.execute(text(f"""
                    INSERT INTO {self.staging_schema}.staged_child_tables_{self.session_id} 
                    (model_name, issue_id, issue_key, row_data, params)
                    VALUES (:model_name, :issue_id, :issue_key, :row_data, :params)
                """), {
                    'model_name': model_name,
                    'issue_id': row.get('issue_id', row.get('id')),  # Some tables use 'id', others 'issue_id'
                    'issue_key': row.get('issue_key', row.get('key')),
                    'row_data': row_json,
                    'params': params_json
                })

        # Commit the batch
        await session.commit()

        # Clear DataFrame from memory immediately
        del df
        gc.collect()

        self.logger.debug(f"Staged {len(record_data['df'])} records for {model_name}")

    async def _process_issues_by_hierarchy(self, session) -> Dict[str, Any]:
        """Process Issues using persistent PostgreSQL tables with hierarchy level ordering"""

        # Get hierarchy level statistics
        result = await session.execute(text(f"""
            SELECT 
                hierarchy_level,
                COUNT(*) as count,
                MIN(staging_id) as min_id,
                MAX(staging_id) as max_id
            FROM {self.staging_schema}.staged_issues_{self.session_id}
            WHERE hierarchy_level IS NOT NULL
            GROUP BY hierarchy_level
            ORDER BY hierarchy_level DESC
        """))

        hierarchy_stats = result.fetchall()
        self.logger.info(f"Issue hierarchy distribution: {[(row[0], row[1]) for row in hierarchy_stats]}")

        if not hierarchy_stats:
            self.logger.warning("No Issues with hierarchy_level found")
            return {'processed': 0, 'failed': 0}

        total_processed = 0
        total_failed = 0

        # Process each hierarchy level from highest to lowest
        for level, count, min_id, max_id in hierarchy_stats:
            self.logger.info(f"Processing hierarchy level {level} ({count} records)")

            # Process this level in batches
            batch_id = 0
            offset = 0

            while True:
                batch_id += 1

                # Get batch for this hierarchy level
                result = await session.execute(text(f"""
                    SELECT staging_id, row_data, params
                    FROM {self.staging_schema}.staged_issues_{self.session_id}
                    WHERE hierarchy_level = :level 
                    AND processed = FALSE
                    ORDER BY 
                        CASE WHEN parent_id IS NULL THEN 0 ELSE 1 END,
                        parent_id NULLS FIRST,
                        staging_id
                    LIMIT :limit OFFSET :offset
                """), {
                    'level': level,
                    'limit': self.chunk_size,
                    'offset': offset
                })

                batch_rows = result.fetchall()
                if not batch_rows:
                    break

                # Convert JSONB data back to DataFrame
                batch_data = []
                params = None
                staging_ids = []

                for staging_id, row_data_json, params_json in batch_rows:
                    row_dict = dict(row_data_json)  # JSONB automatically converts to dict

                    # Convert ISO date strings back to datetime objects if needed
                    for key, value in row_dict.items():
                        if isinstance(value, str) and 'T' in value and (
                                'Z' in value or '+' in value or value.endswith(':00')):
                            try:
                                row_dict[key] = pd.to_datetime(value)
                            except:
                                pass  # Keep as string if conversion fails

                    batch_data.append(row_dict)
                    staging_ids.append(staging_id)

                    if params is None:
                        params = dict(params_json)  # JSONB automatically converts to dict

                if batch_data:
                    batch_df = pd.DataFrame(batch_data)

                    # Use nested savepoint for batch processing
                    savepoint = await session.begin_nested()

                    try:
                        model = self.get_model_by_name('Issue')
                        success = await upsert_async(
                            session,
                            model,
                            batch_df,
                            **params
                        )

                        if success:
                            await savepoint.commit()
                            total_processed += len(batch_df)

                            # Mark batch as processed
                            await session.execute(text(f"""
                                UPDATE {self.staging_schema}.staged_issues_{self.session_id}
                                SET processed = TRUE, batch_id = :batch_id
                                WHERE staging_id = ANY(:staging_ids)
                            """), {
                                'batch_id': batch_id,
                                'staging_ids': staging_ids
                            })

                            self.logger.debug(f"Level {level} batch {batch_id} successful: {len(batch_df)} records")
                        else:
                            await savepoint.rollback()
                            total_failed += len(batch_df)
                            await self._save_failed_batch(batch_df, f"failed_issue_level_{level}_batch_{batch_id}")

                    except Exception as e:
                        await savepoint.rollback()
                        total_failed += len(batch_df)
                        self.logger.error(f"Error in Issue level {level} batch {batch_id}: {e}")
                        await self._save_failed_batch(batch_df, f"error_issue_level_{level}_batch_{batch_id}")

                    del batch_df, batch_data
                    gc.collect()

                offset += self.chunk_size

            # Commit after each level
            await session.commit()
            self.logger.info(f"Completed hierarchy level {level}")

        return {'processed': total_processed, 'failed': total_failed}

    async def _process_child_tables(self, session) -> Dict[str, Any]:
        """Process child tables using persistent PostgreSQL staging"""

        # Get child table statistics
        result = await session.execute(text(f"""
            SELECT model_name, COUNT(*) as count
            FROM {self.staging_schema}.staged_child_tables_{self.session_id}
            GROUP BY model_name
            ORDER BY model_name
        """))

        child_stats = result.fetchall()
        self.logger.info(f"Child table distribution: {[(row[0], row[1]) for row in child_stats]}")

        total_processed = 0
        total_failed = 0
        model_results = {}

        # Define processing order for child tables
        child_table_order = ['WorkLog', 'ChangelogJSON', 'IssueComments', 'IssueLinks']

        # Add any other child tables that exist in the data
        existing_tables = {row[0] for row in child_stats}
        for table in existing_tables:
            if table not in child_table_order:
                child_table_order.append(table)

        for model_name in child_table_order:
            if model_name not in existing_tables:
                continue

            self.logger.info(f"Processing child table: {model_name}")
            model_processed = 0
            model_failed = 0
            batch_id = 0
            offset = 0

            while True:
                batch_id += 1

                # Get batch for this model
                result = await session.execute(text(f"""
                    SELECT staging_id, row_data, params
                    FROM {self.staging_schema}.staged_child_tables_{self.session_id}
                    WHERE model_name = :model_name 
                    AND processed = FALSE
                    ORDER BY staging_id
                    LIMIT :limit OFFSET :offset
                """), {
                    'model_name': model_name,
                    'limit': self.chunk_size,
                    'offset': offset
                })

                batch_rows = result.fetchall()
                if not batch_rows:
                    break

                # Convert JSONB data back to DataFrame
                batch_data = []
                params = None
                staging_ids = []

                for staging_id, row_data_json, params_json in batch_rows:
                    row_dict = dict(row_data_json)

                    # Convert ISO date strings back to datetime objects if needed
                    for key, value in row_dict.items():
                        if isinstance(value, str) and 'T' in value and (
                                'Z' in value or '+' in value or value.endswith(':00')):
                            try:
                                row_dict[key] = pd.to_datetime(value)
                            except:
                                pass

                    batch_data.append(row_dict)
                    staging_ids.append(staging_id)

                    if params is None:
                        params = dict(params_json)

                if batch_data:
                    batch_df = pd.DataFrame(batch_data)

                    savepoint = await session.begin_nested()

                    try:
                        model = self.get_model_by_name(model_name)
                        success = await upsert_async(
                            session,
                            model,
                            batch_df,
                            **params
                        )

                        if success:
                            await savepoint.commit()
                            model_processed += len(batch_df)

                            # Mark batch as processed
                            await session.execute(text(f"""
                                UPDATE {self.staging_schema}.staged_child_tables_{self.session_id}
                                SET processed = TRUE, batch_id = :batch_id
                                WHERE staging_id = ANY(:staging_ids)
                            """), {
                                'batch_id': batch_id,
                                'staging_ids': staging_ids
                            })

                        else:
                            await savepoint.rollback()
                            model_failed += len(batch_df)

                    except Exception as e:
                        await savepoint.rollback()
                        model_failed += len(batch_df)
                        self.logger.error(f"Error in {model_name} batch {batch_id}: {e}")

                    del batch_df, batch_data
                    gc.collect()

                offset += self.chunk_size

            model_results[model_name] = {'processed': model_processed, 'failed': model_failed}
            total_processed += model_processed
            total_failed += model_failed

            # Commit after each model
            await session.commit()
            self.logger.info(f"Completed {model_name}: {model_processed} processed, {model_failed} failed")

        return {
            'processed': total_processed,
            'failed': total_failed,
            'model_results': model_results
        }

    async def process_all_models(self) -> Dict[str, Any]:
        """Process all models with PostgreSQL persistent staging"""
        results = {
            'model_results': {},
            'total_processed': 0,
            'total_failed': 0
        }

        # Use a single long-running session for the entire processing
        async with self.db_rw.async_session() as session:
            try:
                # Phase 1: Process Issues by hierarchy
                self.logger.info("Phase 1: Processing Issues by hierarchy level")
                issue_results = await self._process_issues_by_hierarchy(session)
                results['model_results']['Issue'] = issue_results
                results['total_processed'] += issue_results['processed']
                results['total_failed'] += issue_results['failed']

                # Phase 2: Process child tables
                self.logger.info("Phase 2: Processing child tables")
                child_results = await self._process_child_tables(session)
                results['model_results'].update(child_results['model_results'])
                results['total_processed'] += child_results['processed']
                results['total_failed'] += child_results['failed']

                # Final commit
                await session.commit()

            except Exception as e:
                await session.rollback()
                self.logger.error(f"Error in processing, rolling back: {e}")
                raise

        return results

    async def _save_failed_batch(self, batch_df: pd.DataFrame, filename_prefix: str):
        """Save failed batch for later analysis"""
        try:
            filename = f"{filename_prefix}.xlsx"
            await quick_save_async(batch_df, filename, path="c:/vishal/log/failed_postgres_persistent")
            self.logger.info(f"Saved failed batch to {filename}")
        except Exception as e:
            self.logger.error(f"Failed to save failed batch: {e}")

    async def cleanup_staging_tables(self, session):
        """Drop staging tables after processing"""
        try:
            await session.execute(text(f"DROP TABLE IF EXISTS {self.staging_schema}.staged_issues_{self.session_id}"))
            await session.execute(
                text(f"DROP TABLE IF EXISTS {self.staging_schema}.staged_child_tables_{self.session_id}"))
            await session.commit()
            self.logger.info(f"Cleaned up staging tables for session {self.session_id}")
        except Exception as e:
            self.logger.error(f"Error cleaning up staging tables: {e}")


# UPDATED QUEUE PROCESSOR WITH HIERARCHY AWARENESS
@inject
async def process_upsert_queue_hierarchy_aware(
        q_container: DynamicContainer = Provide[QueueContainer],
        db_rw: DynamicContainer = Provide[ApplicationContainer.database_rw],
        # db_rw: DynamicContainer = Provide[EnhancedApplicationContainer.enhanced_session_manager_rw],
        my_logger: Logger = Provide[LoggerContainer.logger],
        processor_type: str = "streaming",  # "streaming" or "temporary" or "persistent"
        chunk_size: int = 1000,
        staging_schema: str = "staging_temp"
):
    """Process queue with hierarchy-aware Issue processing"""

    none_count = 0
    message_count = 0
    queue_upsert_issue = q_container.queue_selector()["queue_upsert_issue"]


    # Choose processor based on expected volume
    if processor_type == "streaming":
        processor = HierarchyAwareProcessor(db_rw, my_logger, get_model_by_name, chunk_size)
    elif processor_type == "temporary":
        processor = PostgreSQLHierarchyProcessor(db_rw, my_logger, get_model_by_name, chunk_size)
    else:
        processor = PostgreSQLPersistentStagingProcessor(db_rw, my_logger, get_model_by_name, staging_schema)

    # Phase 1: Collect all records
    my_logger.info("Phase 1: Collecting all records from queue")

    while True:
        try:
            upsert_task: dict = await priority_queue_manager.get_priority_message(queue_upsert_issue)
            message_count += 1

            if upsert_task is None:
                none_count += 1
                if none_count >= 25:
                    my_logger.info(f"Received all {none_count} termination signals")
                    break
                elif none_count >= 20:
                    my_logger.info(f"Received {none_count}/25 termination signals, waiting...")
            else:
                # Stage the record
                model = upsert_task["model"]
                df = upsert_task["df"]

                # Data preprocessing
                for col in df.columns:
                    if isinstance(df[col].dtype, pd.DatetimeTZDtype):
                        df[col] = df[col].dt.tz_convert('UTC')
                    if df[col].apply(lambda x: isinstance(x, dict) and len(x) == 0).any():
                        df[col] = df[col].apply(lambda x: None if isinstance(x, dict) and len(x) == 0 else x)
                    if df[col].apply(lambda x: isinstance(x, list) and len(x) == 0).any():
                        df[col] = df[col].apply(lambda x: None if isinstance(x, list) and len(x) == 0 else x)

                if 'id' in df.columns:
                    df_null_ids = df[df["id"].isnull()]
                    if not df_null_ids.empty:
                        await quick_save_async(df_null_ids, f"df_null_ids_{model.__name__}.xlsx",
                                               path=f"c:/vishal/log/null_ids")
                    df = df[df["id"].notnull()]

                record_data = {
                    'model': model,
                    'df': df,
                    'params': {
                        'no_update_cols': upsert_task.get("no_update_cols", ()),
                        'on_conflict_update': upsert_task.get("on_conflict_update", True),
                        'conflict_condition': upsert_task.get("conflict_condition", None),
                        'message_count': message_count,
                        'my_logger': my_logger
                    }
                }

                await processor.add_record(model.__name__, record_data)
                my_logger.debug(f"Staged record {message_count} for {model.__name__}")

        except Exception as e:
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_num = exc_tb.tb_lineno
            tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
            my_logger.exception(
                f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
                exc_info=True
            )
            my_logger.error(f"Error in collection phase: {e}", exc_info=True)
        finally:
            if 'upsert_task' in locals():
                queue_upsert_issue.task_done()

    # Phase 2: Process all records with hierarchy awareness
    my_logger.info("Phase 2: Processing all records with hierarchy awareness")

    try:
        results = await processor.process_all_models()

        my_logger.info(f"Processing complete:")
        my_logger.info(f"  Total successful: {results['total_processed']}")
        my_logger.info(f"  Total failed: {results['total_failed']}")

        for model_name, model_result in results['model_results'].items():
            my_logger.info(f"  {model_name}: {model_result['processed']} success, {model_result['failed']} failed")

    except Exception as e:
        my_logger.error(f"Error in processing phase: {e}", exc_info=True)
        raise

    return results





@inject
async def process_upsert_queue(
    # pg_async_session,
    # queue_upsert_issue: Queue,
    # my_logger: Logger|None = None,
        project_key: str,
        q_container: DynamicContainer = Provide[QueueContainer],
        # db_rw: DynamicContainer = Provide[ApplicationContainer.database_rw],
        app_container: DynamicContainer = Provide[EnhancedApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
):

    none_count = 0
    model = None
    idle_timeout = 120.0  # 2 minutes of no activity before considering shutdown
    expected_none_count = 25  # 5 consumers × 5 sub-consumers each
    message_count = 0
    queue_upsert_issue = q_container.queue_selector()["queue_upsert_issue"]
    # Start with initial session
    # session = db_rw.async_session()
    # current_session = await session.__aenter__()
    # current_transaction = await current_session.begin()

    async with app_container.base_session_manager_rw().update_schema(project_key).async_session() as pg_async_session:
        async with pg_async_session.begin():
            while True:
                try:
                    upsert_task: dict = await priority_queue_manager.get_priority_message(queue_upsert_issue)
                    message_count += 1
                    my_logger.debug(f"message_read = {message_count}")

                    if upsert_task is None:
                        none_count += 1
                        my_logger.debug(f"none_count in queue_upsert_issue = {none_count}")

                        # More flexible termination condition
                        if none_count >= expected_none_count:
                            my_logger.info(f"Received all {none_count} termination signals, shutting down upsert queue processor")
                            break
                        elif none_count >= 20:  # If we get at least 20 out of 25 expected signals
                            # Wait a bit more to see if remaining signals arrive
                            my_logger.info(f"Received {none_count}/{expected_none_count} termination signals, waiting for remaining...")
                    else:
                        # Process actual data
                        model = upsert_task["model"]
                        df = upsert_task["df"]
                        for col in df.columns:
                            if isinstance(df[col].dtype, pd.DatetimeTZDtype):
                                df[col] = df[col].dt.tz_convert('UTC')
                            if df[col].apply(lambda x: isinstance(x, dict) and len(x) == 0).any():
                                df[col] = df[col].apply(lambda x: None if isinstance(x, dict) and len(x) == 0 else x)
                            if df[col].apply(lambda x: isinstance(x, list) and len(x) == 0).any():
                                df[col] = df[col].apply(lambda x: None if isinstance(x, list) and len(x) == 0 else x)

                        if 'id' in df.columns:
                            df_null_ids = df[df["id"].isnull()]
                            if not df_null_ids.empty:
                                _ = await quick_save_async(df_null_ids, f"df_null_ids_{model.__name__}.xlsx", path=f"c:/vishal/log/null_ids")

                            df = df[df["id"].notnull()]

                        no_update_cols = upsert_task.get("no_update_cols", ())
                        on_conflict_update = upsert_task.get("on_conflict_update", True)
                        conflict_condition = upsert_task.get("conflict_condition", None)
                        my_logger.debug(f"Upserting {message_count} model= {model.__name__}")

                        success = await upsert_async(
                            pg_async_session, model, df,
                            no_update_cols=no_update_cols,
                            on_conflict_update=on_conflict_update,
                            conflict_condition=conflict_condition,
                            message_count=message_count,
                            my_logger=my_logger
                        )
                        if not success:
                            my_logger.error(f"Failed to upsert {model.__name__} {message_count}")
                        else:
                            my_logger.debug(f"Upserted {model.__name__} {message_count}")
                except Exception as err:
                    exc_type, exc_value, exc_tb = sys.exc_info()
                    line_num = exc_tb.tb_lineno
                    tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
                    my_logger.exception(
                        f"Line {line_num} Error processing upsert task: {''.join(tb.format_exception_only())}",
                        exc_info=True
                    )
                    await pg_async_session.rollback()




    my_logger.info(f"process_upsert_queue shutdown complete. Final queue size: {queue_upsert_issue.qsize()}")
    await _cleanup_orphaned_parent_references(pg_async_session, my_logger)


@inject
def handle_exception(e, my_logger: Logger = Provide[LoggerContainer.logger]):
    exc_type, exc_value, exc_tb = sys.exc_info()
    line_num = exc_tb.tb_lineno
    tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
    my_logger.exception(
        f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
        exc_info=True
    )
    raise e

@dataclass
class ConsumerConfig:
    """Configuration object to reduce parameter passing"""
    consumer_id: int
    project_key: str
    names: list
    name: str
    num_producers: int = 1


@dataclass
class QueueManager:
    """Encapsulates queue operations and naming"""
    q_container: any  # QueueContainer from dependency_injector
    project_key: str
    consumer_name: str
    consumer_id: int = 0

    def __post_init__(self):
        """Configure the queue container with the schema name"""
        self.q_container.config.override({"schema_name": self.project_key})

    def get_queues(self) -> dict:
        """Get the queue dictionary for the current schema"""
        return self.q_container.queue_selector()

    def get_task_name(self, task_type: str) -> str:
        """Generate standardized task names"""
        return f"{task_type}_{self.project_key}_{self.consumer_name.lower()}"

    async def send_termination_signals(self):
        """Send None signals to all queues to terminate consumers"""
        queues = self.get_queues()
        await priority_queue_manager.put_priority_message(
            queues["queue_changelog"], None, MessageType.TERMINATION, self.consumer_id
        )
        await priority_queue_manager.put_priority_message(
            queues["queue_worklog"], None, MessageType.TERMINATION, self.consumer_id
        )
        await priority_queue_manager.put_priority_message(
            queues["queue_comment"], None, MessageType.TERMINATION, self.consumer_id
        )
        await priority_queue_manager.put_priority_message(
            queues["queue_issue_links"], None, MessageType.TERMINATION, self.consumer_id
        )
        await priority_queue_manager.put_priority_message(
            queues["queue_issue"], None, MessageType.TERMINATION, self.consumer_id
        )


def normalize_issue_fields(df_issue: pd.DataFrame) -> pd.DataFrame:
    """Normalize fields and remove extracted data"""
    # Remove worklog and comment from fields after extraction
    df_issue['fields'] = df_issue['fields'].apply(
        lambda x: {k: v for k, v in x.items() if k not in ['worklog', 'comment']}
    )

    # Normalize fields into columns
    return df_issue.join(pd.json_normalize(df_issue.fields)).drop(columns=["fields"])


def process_description_markdown(df_issue: pd.DataFrame):
    """Extract description markdown from rendered fields"""
    df_issue['description_markdown'] = df_issue['renderedFields'].apply(
        lambda x: x.get('description', '') if isinstance(x, dict) else None
    )


def cleanup_issue_dataframe(df_issue: pd.DataFrame):
    """Remove unnecessary columns from issue dataframe"""
    columns_to_drop = [
        'changelog', 'expand', 'self', 'worklog', 'comment', 'renderedFields'
    ]
    existing_columns = [col for col in columns_to_drop if col in df_issue.columns]
    df_issue.drop(columns=existing_columns, inplace=True)


class IssueProcessor:
    """Handles issue data processing and distribution to queues"""

    def __init__(self, queue_manager: QueueManager, logger: Logger, consumer_id: int = 0):
        self.queue_manager = queue_manager
        self.logger = logger
        self.consumer_id = consumer_id

    async def process_issue_batch(self, issues: list):
        """Process a batch of issues and distribute to appropriate queues"""
        if not issues:
            return

        df_issue = pd.DataFrame(issues)


        # Extract and queue changelog data
        await self._process_changelog(df_issue)

        # Extract and queue worklog data
        await self._process_worklog(df_issue)

        # Extract and queue comment data
        await self._process_comment(df_issue)

        # Process main issue data
        df_issue = normalize_issue_fields(df_issue)

        # Extract and queue issue links
        await self._process_issue_links(df_issue)

        # Process description markdown
        process_description_markdown(df_issue)

        # Clean up issue dataframe
        cleanup_issue_dataframe(df_issue)

        # Queue the processed issue data
        queues = self.queue_manager.get_queues()
        await priority_queue_manager.put_priority_message(
            queues["queue_issue"],
            df_issue,
            MessageType.REGULAR_DATA,
            self.consumer_id
        )

        self.logger.debug(f"Processed {df_issue.shape[0]} records")

    async def _process_changelog(self, df_issue: pd.DataFrame):
        """Extract and queue changelog data"""
        df_changelog = df_issue[["id", "key", 'changelog']].copy()
        queues = self.queue_manager.get_queues()
        await priority_queue_manager.put_priority_message(
            queues["queue_changelog"],
            df_changelog,
            MessageType.REGULAR_DATA,
            self.consumer_id
        )

    async def _process_worklog(self, df_issue: pd.DataFrame):
        """Extract and queue worklog data"""
        asyncio.current_task().set_name("process_worklog")
        df_issue['worklog'] = df_issue['fields'].apply(lambda x: x.get('worklog'))

        # df_worklog = df_issue[['worklog', "id", "key"]].copy()
        df_worklog = df_issue[['worklog', "key"]].copy()
        # timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        # _ = await quick_save_async(df_issue, f"df_worklog_process_worklog_{timestamp}.xlsx", path="c:/vishal/log/worklog")
        queues = self.queue_manager.get_queues()
        await priority_queue_manager.put_priority_message(
            queues["queue_worklog"],
            df_worklog,
            MessageType.REGULAR_DATA,
            self.consumer_id
        )

    async def _process_comment(self, df_issue: pd.DataFrame):
        """Extract and queue comment data"""
        df_issue['comment'] = df_issue['fields'].apply(lambda x: x.get('comment'))
        df_comment = df_issue[['comment', "id", "key"]].copy()
        queues = self.queue_manager.get_queues()
        await priority_queue_manager.put_priority_message(
            queues["queue_comment"],
            df_comment,
            MessageType.REGULAR_DATA,
            self.consumer_id
        )

    async def _process_issue_links(self, df_issue: pd.DataFrame):
        """Extract and queue issue links if they exist"""
        if 'issuelinks' not in df_issue.columns:
            return

        df_issue_links = df_issue[['issuelinks', "id", "key"]].copy()
        df_issue_links.rename(columns={"id": "issue_id", "key": "issue_key"}, inplace=True)
        df_issue.drop(columns=['issuelinks'], inplace=True)

        # Filter out empty issuelinks
        df_issue_links = df_issue_links[
            df_issue_links['issuelinks'].apply(lambda x: len(x) > 0)
        ]

        if not df_issue_links.empty:
            queues = self.queue_manager.get_queues()
            await priority_queue_manager.put_priority_message(
                queues["queue_issue_links"],
                df_issue_links,
                MessageType.REGULAR_DATA,
                self.consumer_id
            )


class ConsumerTaskManager:
    """Manages the creation and coordination of consumer tasks"""

    def __init__(self, config: ConsumerConfig, queue_manager: QueueManager,
                 # pg_async_session,
                 http_session: aiohttp.ClientSession, logger: Logger):
        self.config = config
        self.queue_manager = queue_manager
        # self.pg_async_session = pg_async_session
        self.http_session = http_session
        self.logger = logger

    def create_consumer_tasks(self, task_group):
        """Create all consumer tasks"""
        queues = self.queue_manager.get_queues()

        tasks = [
            ("consume_changelog", consume_changelog, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_changelog"),
                queues["queue_changelog"],
                queues["queue_upsert_issue"],
                self.config.project_key,
                # self.pg_async_session,
                self.http_session,
                self.logger,
            ]),
            ("consume_worklog", consume_worklog, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_worklog"),
                queues["queue_worklog"],
                queues["queue_upsert_issue"],
                self.config.project_key,
                # self.pg_async_session,
                self.http_session,
                self.logger,
            ]),
            ("consume_comment", consume_comment, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_comment"),
                queues["queue_comment"],
                queues["queue_upsert_issue"],
                self.config.project_key,
                # self.pg_async_session,
                self.http_session,
                self.logger,
            ]),
            ("consume_issue_links", consume_issue_links, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_issue_links"),
                queues["queue_issue_links"],
                queues["queue_upsert_issue"],
                self.config.project_key,
                # self.pg_async_session,
                self.http_session,
                self.logger
            ]),
            ("consume_issue", consume_issue, [
                self.config.consumer_id,
                self.queue_manager.get_task_name("consume_issue"),
                queues["queue_issue"],
                queues["queue_upsert_issue"],
                self.config.project_key,
                # self.pg_async_session,
                self.http_session,
                self.logger,
            ]),
        ]

        for task_type, function_name, args in tasks:
            task_group.create_task(
                function_name(*args),
                name=self.queue_manager.get_task_name(task_type)
            )



class TerminationController:
    """Handles the termination logic for consumers"""

    def __init__(self, num_producers: int, queue_manager: QueueManager, logger: Logger):
        self.num_producers = num_producers
        self.queue_manager = queue_manager
        self.logger = logger
        self.none_count = 0

    async def handle_none_signal(self, consumer_id: int, name: str) -> bool:
        """Handle None signal and return True if it should terminate"""
        self.none_count += 1
        queues = self.queue_manager.get_queues()

        self.logger.debug(
            f"None found in {name} None count: {self.none_count}/{self.num_producers}. "
            f"QSize = {queues['queue_issues'].qsize()}"
        )

        # Wait for all producers to send their None signals
        # Using the same logic as original: count_none == 2 (hardcoded check)
        if self.none_count == 2:  # Keeping original logic
            self.logger.info(f"Consumer {consumer_id} sending termination signals to all queues")
            await self.queue_manager.send_termination_signals()
            return True

        return False


async def _process_issues_loop(
        queue_manager: QueueManager,
        issue_processor: IssueProcessor,
        termination_controller: TerminationController,
        consumer_id: int,
        name: str,
        logger: Logger
):
    """Separated main processing loop for clarity"""
    queues = queue_manager.get_queues()
    queue_issues_task_done = 0

    logger.debug("Starting main processing loop")

    while True:
        try:
            # Debug queue get operation
            # async with debug_queue_operation(queues["queue_issues"], "get", "queue_issues") as item:
            #     issue_batch: Optional[list] = item

            issue_batch: Optional[list] = await priority_queue_manager.get_priority_message(
                queues["queue_issues"]
            )
            queue_issues_task_done += 1

            if issue_batch is None:
                logger.debug(f"{consumer_id} & {name}: Found None in queue_issues. QSize = {queues['queue_issues'].qsize()}")
                should_terminate = await termination_controller.handle_none_signal(
                    consumer_id, name
                )
                if should_terminate:
                    logger.debug(f"Consumer {consumer_id} & {name} breaking from loop")
                    break
                continue

            # Process the issue batch
            await issue_processor.process_issue_batch(issue_batch)

        except Exception as e:
            handle_exception(e)  # Assuming this function exists
        finally:
            logger.debug(f"{name} Queue size queue_issues: {queues['queue_issues'].qsize()}")
            queues["queue_issues"].task_done()
            queue_issues_task_done -= 1
            logger.debug(f"queue_issues_task_done = {queue_issues_task_done}")
            # Debug task_done operation
            # async with debug_queue_operation(queues["queue_issues"], "task_done", "queue_issues"):
            #     pass



async def consume_issues(
        consumer_id: int,
        project_key: str,
        # pg_async_session,
        http_session: aiohttp.ClientSession,
        names: list,
        name: str,
        q_container: DynamicContainer,
        my_logger: Logger,
        num_producers: int = 1,
):
    """Main consumer function - focused on orchestration"""
    asyncio.current_task().set_name(f"consume_issues_{name}")
    my_logger.debug(f"consumer_id {consumer_id}, name={names[consumer_id]}")

    # Register this consumer process for monitoring
    global_async_process_tracker.register_process(name, "consumer", project_key)

    # Create configuration and supporting objects
    config = ConsumerConfig(consumer_id, project_key, names, name, num_producers)

    # Create supporting objects - QueueManager will handle config override
    queue_manager = QueueManager(q_container, project_key, names[consumer_id], consumer_id)
    issue_processor = IssueProcessor(queue_manager, my_logger, consumer_id)
    task_manager = ConsumerTaskManager(
        config, queue_manager,
        # pg_async_session,
        http_session, my_logger
    )
    termination_controller = TerminationController(num_producers, queue_manager, my_logger)

    async with asyncio.TaskGroup() as tg:
        try:
            # Create all consumer tasks
            task_manager.create_consumer_tasks(tg)

            # Main processing loop
            await _process_issues_loop(
                queue_manager, issue_processor, termination_controller,
                consumer_id, name, my_logger
            )

            my_logger.debug(f"{project_key} Processing completed!")

        except* TypeError as te:
            for error in te.exceptions:
                my_logger.error(f"TypeError: {error}")
        except* Exception as ex:
            my_logger.error(f"Exception: {ex.exceptions}. Requesting shutdown.")
            request_shutdown()
            handle_exception(ex)
        finally:
            my_logger.debug("consume_issues task group completed")

    my_logger.info(f"{consumer_id} {name} consume_issues finished!")

    # Mark process as completed
    global_async_process_tracker.update_process(name, "completed")


@inject
async def get_issues_from_jira_jql(
        project_key: str,
        scope: str,
        batch_start_time,
        http_session: aiohttp.ClientSession, base_url: str, url: str, name: str,
        jql: str, total_records: int, producer_count: int,
        my_logger: Logger = Provide[LoggerContainer.logger],
        q_container: DynamicContainer = Provide[QueueContainer],
        fields_container: FieldNameExtractor = Provide[IssueFieldsContainer.field_name_extractor]
):
    global commit_transaction
    q_container.config.override({"schema_name": f"{project_key}"})
    my_logger.info(f"Called get_issues_from_jira_jql")

    # Extract task_id from name (format: "producer_{task_id}")
    task_id = int(name.split('_')[-1]) if '_' in name else 0

    # Register this producer process for monitoring
    global_async_process_tracker.register_process(name, "producer", project_key)

    fields: list = fields_container.get_field_names()

    loop_count = 0

    try:
        # response = await fetch_with_retries_post(
        #     http_session,
        #     f"{base_url}/rest/api/3/search/approximate-count",
        #     json_payload={"jql": jql}
        # )
        # total_records = response['result']['count']
        # my_logger.dataframe_utils(f"total_records = {total_records}")
        # jqls = await split_jql_by_count(jql)
        # my_logger.dataframe_utils(f"jqls = {jqls}")

        # issue_start_at = 0
        payload = {
            "fields": fields,
            "fieldsByKeys": True,
            "jql": jql,
            "maxResults": 100,
            "expand": "changelog,renderedFields"
        }

        while True:
            response = await fetch_with_retries_post(http_session, url, payload)

            if response.get('success'):
                result = response.get('result', {})
                issues = result.get('issues', [])
                record_counts = len(issues)
                my_logger.debug(f"{name} - record_counts = {record_counts}, total_records = {total_records}")

                # Update process tracking
                global_async_process_tracker.update_process(name, "running", record_counts)

                if record_counts > 0:
                    await priority_queue_manager.put_priority_message(
                        q_container.queue_selector()["queue_issues"],
                        issues,
                        MessageType.REGULAR_DATA,
                        task_id
                    )
                    await priority_queue_manager.put_priority_message(
                        q_container.queue_selector()["queue_stats"],
                        {
                            'isLast': False,
                            'process_time': 0,
                            'elapsed_time': 0,
                            'record_count': record_counts,
                            'total': total_records,
                            'producer_id': task_id,
                            'name': name,
                            'producer_count': producer_count
                        },
                        MessageType.REGULAR_DATA,
                        task_id
                    )

                if not result.get('nextPageToken'):
                    my_logger.info("nextPageToken not found. Exiting!!")
                    break
                else:
                    next_page_token = result.get('nextPageToken')
                    payload['nextPageToken'] = next_page_token
            else:
                # Handle failure case
                my_logger.error(f"Failed to fetch issues: {response.get('exception')}")
                break

            loop_count += 1
        my_logger.info(f"Completed get_issues_from_jira_jql")

    except Exception as e:
        handle_exception(e)
    finally:

        iterations = max(1, 10 // producer_count)
        my_logger.info(f"producer_count = {producer_count}")
        my_logger.info(f"iterations = {iterations}")
        my_logger.info(f"{name} total_records = {total_records}")
        for i in range(iterations):
            my_logger.debug(f"{i+1}. Adding None end of queue signal to queue_issues & queue_stats")
            await priority_queue_manager.put_priority_message(
                q_container.queue_selector()["queue_issues"],
                None,
                MessageType.TERMINATION,
                task_id
            )
            await priority_queue_manager.put_priority_message(
                q_container.queue_selector()["queue_stats"],
                {
                    'isLast': True,
                    'process_time': 0,
                    'elapsed_time': 0,
                    'record_count': 0,
                    'total': total_records,
                    'name': name,
                    'producer_count': producer_count
                },
                MessageType.TERMINATION,
                task_id
            )
        my_logger.info(f"Completed get_issues_from_jira")

        # Mark process as completed
        global_async_process_tracker.update_process(name, "completed")


@inject
async def get_version_related_details(
        session, url, semaphore: asyncio.locks.Semaphore,
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    asyncio.current_task().set_name("get_version_related_details")

    async with semaphore:
        async with session.request(
                "GET",
                url,
        ) as resp:
            try:
                if resp.status == 200:

                    response = await resp.json()
                    if 'relatedwork' in url:
                        for item in response:
                            item['self'] = url
                    return {"success": True, "result": response}

                else:
                    my_logger.error(f"Request to {url} failed with status {resp.status}")
                    return {"success": False, "exception": f"HTTP {resp.status}"}

            except Exception as e:
                my_logger.error(f"Error processing response from {url}: {e}")
                return {"success": False, "exception": e}


@inject
async def process_jira_versions(
        project_key: str,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],
        progress_callback=None
):
    # config_dict = get_env_variables()
    my_logger.debug("Started process_jira_versions")
    semaphore = asyncio.Semaphore(30)
    task_name = f"process_jira_versions_{project_key}"
    asyncio.current_task().set_name(task_name)
    app_container.schema.override(project_key)

    # Register for monitoring
    global_async_process_tracker.register_process(task_name, "task", project_key)
    timeout = aiohttp.ClientTimeout(total=300)
    connector = aiohttp.TCPConnector(
        limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
        ssl=ssl.create_default_context()
    )

    async with (aiohttp.ClientSession(
        headers=jira_entry.custom_properties,        timeout=timeout,
        connector=connector, raise_for_status=True
    ) as http_session):
        # response = await fetch_versions(http_session,  project_key.upper())

        response = await fetch_with_retries_get(
            http_session, url=f"{jira_entry.url}/rest/api/3/project/{project_key.upper()}/versions"
        )

        # Separate successes and exceptions
        successful_responses = response.get("result", []) if response.get("success") else []
        exceptions = response.get("exception") if not response.get("success") else None

        my_logger.debug(f"# of successful_responses: {len(successful_responses) if successful_responses else 0}")
        if exceptions:
            my_logger.debug(f"Exception occurred: {exceptions}")
        else:
            my_logger.debug("No exceptions occurred")

        if response.get("success") and successful_responses:
            df = pd.DataFrame(successful_responses)
            df['relatedIssueCounts'] = df['self'] + '/relatedIssueCounts'
            df['relatedwork'] = df['self'] + '/relatedwork'
            df['unresolvedIssueCount'] = df['self'] + '/unresolvedIssueCount'
            ret_string = f'+{df.shape[0]}'

            # Update progress tracking
            global_async_process_tracker.update_process(task_name, "running", df.shape[0])
            if progress_callback:
                progress_callback(25, f"Processing {df.shape[0]} versions")
            try:
                task_related_issue_counts = [
                    create_task(
                        get_version_related_details(
                            http_session, row,
                            semaphore
                        ),
                        name=f"task_relatedIssueCounts_{num2words(i, ordinal=True)}"
                    )
                    for i, row in enumerate(df['relatedIssueCounts'].tolist())
                ]

                task_related_work = [
                    create_task(
                        get_version_related_details(
                            http_session, row,
                            semaphore
                        ),
                        name=f"task_relatedwork_{num2words(i, ordinal=True)}"
                    ) for i, row in enumerate(df['relatedwork'].tolist())
                ]

                task_unresolved_issue_count = [
                    create_task(
                        get_version_related_details(
                            http_session, row,
                            semaphore
                        ),
                        name=f"task_unresolvedIssueCount_{num2words(i, ordinal=True)}"
                    ) for i, row in enumerate(df['unresolvedIssueCount'].tolist())
                ]

                # Start both sets of tasks concurrently
                tasks = task_related_issue_counts + task_related_work + task_unresolved_issue_count
                results = await asyncio.gather(*tasks)

                # Separate successful and failed responses
                successful_related_issue_counts = [
                    result["result"] for result in results[:len(task_related_issue_counts)] if result.get("success")
                ]
                successful_related_work = [
                    result["result"] for result in
                    results[len(task_related_issue_counts):len(task_related_issue_counts) + len(task_related_work)] if
                    result.get("success")
                ]
                successful_unresolved_issue_count = [
                    result["result"] for result in results[len(task_related_issue_counts) + len(task_related_work):] if
                    result.get("success")
                ]

                exceptions = [
                    result["exception"] for result in results if not result.get("success")
                ]

                # Log exceptions
                for exception in exceptions:
                    my_logger.error(f"Task failed with exception: {exception}")

                # Normalize successful responses into dataframes
                if successful_related_issue_counts:
                    df_related = pd.json_normalize(successful_related_issue_counts)
                else:
                    df_related = pd.DataFrame()

                if successful_related_work:
                    df_related_work = pd.json_normalize(
                        [item for sublist in successful_related_work for item in sublist])
                else:
                    df_related_work = pd.DataFrame()

                if successful_unresolved_issue_count:
                    df_unresolved_issue_count = pd.json_normalize(successful_unresolved_issue_count)
                else:
                    df_unresolved_issue_count = pd.DataFrame()

                # # Split the results back into their respective lists
                # task_related_issue_counts = results[:len(task_related_issue_counts)]
                # task_related_work = results[
                #                     len(task_related_issue_counts):len(task_related_issue_counts) +
                #                     len(task_related_work)
                #                     ]
                # task_unresolved_issue_count = results[len(task_related_issue_counts) + len(task_related_work):]
                #
                # # Filter out empty lists
                # filtered_data = [item for sublist in task_related_work if sublist for item in sublist if sublist]
                #
                # df_related = pd.json_normalize(task_related_issue_counts)
                # df_related_work = pd.json_normalize(filtered_data)
                # df_unresolved_issue_count = pd.json_normalize(task_unresolved_issue_count)

                for data_frame in [df_related, df_unresolved_issue_count]:
                    data_frame['id'] = data_frame['self'].apply(lambda x: x.split('/')[-1])
                    data_frame.drop(columns=["self"], inplace=True)
                if df_related_work.shape[0] > 0:
                    df_related_work['id'] = df_related_work['self'].apply(lambda x: x.split('/')[-2])
                    df_related_work.drop(columns=["self"], inplace=True)

                df.drop(
                    columns=[
                        'relatedIssueCounts', 'relatedwork', 'unresolvedIssueCount',
                        'self'
                    ],
                    inplace=True
                )
                df = pd.merge(df, df_related, how='left', on='id')
                df = pd.merge(df, df_unresolved_issue_count, how='left', on='id')
                if df_related_work.shape[0] > 0:
                    df = pd.merge(df, df_related_work, how='left', on='id')

                with app_container.database_rw().update_schema(project_key).session() as pg_session:
                    upsert(pg_session, Versions, df, )
                    pg_session.commit()

                # Mark as completed
                global_async_process_tracker.update_process(task_name, "completed", df.shape[0])
                if progress_callback:
                    progress_callback(100, f"Completed: {df.shape[0]} versions")

                return ret_string
            except Exception as e:
                global_async_process_tracker.update_process(task_name, "failed")
                handle_exception(e)
        else:
            for exception in exceptions:
                my_logger.error(f"Task failed with exception: {exception}")
            return "UNKNOWN EXCEPTION"


@inject
async def process_jira_issues(
        project_key: str,
        scope: str,
        initial_load: bool,
        app_container: DeclarativeContainer = Provide[ApplicationContainer],
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        q_container: DynamicContainer = Provide[QueueContainer],
        my_logger: Logger = Provide[LoggerContainer.logger],
):
    """
    Process JIRA issues with improved error handling, transaction management,
    and better separation of concerns.
    """
    try:
        my_logger.info(f"Processing project: {project_key}, scope: {scope}")
        # Initialize timezone and process marker
        time_zone, process_start_marker = await _initialize_jira_session(
            jira_entry, my_logger
        )

        # Setup database schema and get last run datetime
        app_container.schema.override(project_key)
        q_container.config.override({"schema_name": project_key})

        async with app_container.database_rw_enhanced().update_schema(project_key).async_session() as pg_session:
            last_run_datetime = await _get_last_run_datetime(
                pg_session, time_zone, my_logger
            )

        # Build and execute JQL query
        jql = _build_jql_query(project_key, scope, last_run_datetime)
        if not jql:
            my_logger.warning(f"No valid JQL generated for scope: {scope}")
            return

        # Process issues with proper transaction handling
        success = await _process_issues_with_producers_consumers(
            project_key=project_key,
            scope=scope,
            jql_query=jql,
            last_run_datetime=last_run_datetime,
            # pg_session=pg_session,
            jira_entry=jira_entry,
            q_container=q_container,
            my_logger=my_logger
        )
        my_logger.info(f"process_jira_issues_with_producers_consumers completed for {project_key}. Status = {success}")

        # Handle parent-child relationship cleanup
        # await _cleanup_orphaned_parent_references(pg_session, my_logger)
        async with app_container.database_rw_enhanced().update_schema(project_key).async_session() as pg_session:
            if success and scope == "project":
                await _update_run_details(pg_session, process_start_marker, my_logger)


        # Commit transaction based on success
        # if success:
        #     await pg_session.commit()
        #     my_logger.info(f"Transaction committed successfully for {project_key}")
        #
        #     # Update run details for project scope
        #     if scope == "project":
        #         await _update_run_details(pg_session, process_start_marker, my_logger)
        # else:
        #     await pg_session.rollback()
        #     my_logger.error(f"Transaction rolled back due to errors for {project_key}")

    except Exception as e:
        my_logger.error(f"Fatal error in process_jira_issues for {project_key}: {str(e)}")
        handle_exception(e)
        raise
    finally:
        my_logger.info(f"process_jira_issues completed for {project_key}")


async def _initialize_jira_session(jira_entry: EntryDetails, my_logger: Logger) -> tuple:
    """Initialize JIRA session and get timezone information."""

    endpoint = "/rest/api/3/myself"
    url = f"{jira_entry.url}{endpoint}"
    timeout = aiohttp.ClientTimeout(total=300)
    connector = aiohttp.TCPConnector(
        limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
        ssl=ssl.create_default_context()
    )
    async with aiohttp.ClientSession(
        headers=jira_entry.custom_properties,        timeout=timeout,
        connector=connector, raise_for_status=True
    ) as session:
        response = await fetch_with_retries_get(session, url)
        if response.get("success") and response.get('result'):
            timezone_str = response['result'].get('timeZone', 'UTC')
            try:
                time_zone = ZoneInfo(timezone_str)
            except Exception:
                time_zone = ZoneInfo('UTC')
                my_logger.warning(f"Invalid response '{response}' from JIRA. Using UTC as fallback")
        else:
            my_logger.warning(f"Failed to get user info from JIRA: {response.get('exception', 'Unknown error')}")
            my_logger.warning("Using UTC timezone as fallback")
            time_zone = ZoneInfo('UTC')
        process_start_marker = datetime.now(tz=time_zone)

    return time_zone, process_start_marker


async def _get_last_run_datetime(
        pg_session,
        time_zone: ZoneInfo,
        my_logger: Logger
) -> datetime:
    """Get the last run datetime or return default if not found."""
    stmt = select(RunDetailsJira.last_run).filter(RunDetailsJira.topic == "Issue")

    try:
        result = await pg_session.execute(stmt)
        local_datetime = result.scalar_one()
        my_logger.info(f"Last run datetime: {local_datetime}")
        data_load.set("incremental")
        return local_datetime

    except sqlalchemy.exc.NoResultFound:
        # Default to a very old date for initial load
        default_datetime = datetime(2010, 1, 1, 0, 0, 0, tzinfo=time_zone)
        my_logger.debug(f"No previous run found, using default: {default_datetime}")
        data_load.set("initial")
        return default_datetime

    except Exception as e:
        my_logger.error(f"Error getting last run datetime: {str(e)}")
        raise


def _build_jql_query(project_key: str, scope: str, last_run_datetime: datetime) -> str:
    """Build JQL query based on scope and load type."""
    project_upper = project_key.upper()
    datetime_str = last_run_datetime.strftime('%Y-%m-%d %H:%M')

    if scope == "project":
        if data_load.get() == "initial":
            jql = f"""
                project = {project_upper} 
                AND created > '{datetime_str}' 
                ORDER BY created ASC
            """
        else:
            jql = f"""
                project = {project_upper} 
                AND updated > '{datetime_str}' 
                ORDER BY updated ASC
            """
    elif scope == "recon":
        jql = f"""
            project = {project_upper} 
            AND key in (
            PLAT-166896           
            )
        """
    else:
        return ""
    # , PLAT-166344 , PLAT - 165979, PLAT - 171550

    # Normalize whitespace
    return ' '.join(jql.split())



async def _process_issues_with_producers_consumers(
        project_key: str,
        scope: str,
        jql_query: str,
        last_run_datetime: datetime,
        # pg_session,
        jira_entry: EntryDetails,
        q_container: DynamicContainer,
        my_logger: Logger
) -> Union[ bool|None]:
    """Process issues using producer-consumer pattern with proper error handling."""
    asyncio.current_task().set_name(f"process_issues_with_producers_consumers_{project_key}_{scope}")

    # Configuration
    CONSUMER_COUNT = 5
    FRUIT_NAMES = ["apple", "banana", "cherry", "dates", "fig"]


    async def patched_start(self):
        self._reuse_port = False
        return await _original_start(self)

    # Monkey patch to avoid reuse_port on Windows
    _original_start = aiohttp.web_runner.TCPSite.start
    aiohttp.web_runner.TCPSite.start = patched_start


    try:
        # Split JQL into multiple queries for parallel processing
        jql_queries, total_records_estimate = await split_jql_by_count(jql_query)
        my_logger.debug(f"jql_queries: {jql_queries}")
        my_logger.debug(f"total_records_estimate: {total_records_estimate}")

        if not jql_queries:
            my_logger.warning("No JQLs returned, using original query")
            jql_queries = [jql_query]
            total_records_estimate = -1

        num_producers = len(jql_queries)
        my_logger.info(f"Starting {num_producers} producers and {CONSUMER_COUNT} consumers")
        timeout = aiohttp.ClientTimeout(total=900)
        connector = aiohttp.TCPConnector(
            limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
            ssl=ssl.create_default_context()
        )
        async with aiohttp.ClientSession(
            headers=jira_entry.custom_properties,                timeout=timeout,
            connector=connector, raise_for_status=True
        ) as http_session:
            with aiomonitor.Monitor(loop=asyncio.get_running_loop(),webui_port=9999):
                async with asyncio.TaskGroup() as task_group:
                    # Create producer tasks
                    for task_id, jql_query in enumerate(jql_queries):
                        task_group.create_task(
                            get_issues_from_jira_jql(
                                project_key=project_key,
                                scope=scope,
                                batch_start_time=last_run_datetime,
                                http_session=http_session,
                                base_url=jira_entry.url,
                                url=f"{jira_entry.url}/rest/api/3/search/jql",
                                name=f"producer_{task_id}",
                                jql=jql_query,
                                total_records=total_records_estimate,
                                producer_count=num_producers,
                            ),
                            name=f"producer_{task_id}"
                        )

                    # Create consumer tasks
                    for consumer_id in range(CONSUMER_COUNT):
                        consumer_name = f"consumer_{project_key}_{FRUIT_NAMES[consumer_id]}"
                        task_group.create_task(
                            consume_issues(
                                consumer_id=consumer_id,
                                project_key=project_key,
                                # pg_async_session=pg_session,
                                http_session=http_session,
                                names=FRUIT_NAMES,
                                name=consumer_name,
                                q_container=q_container,
                                my_logger=my_logger,
                                num_producers=num_producers,

                            ),
                            name=consumer_name
                        )
                    task_group.create_task(
                        process_upsert_queue(project_key=project_key),
                        # "streaming" or "temporary" or "persistent"
                        # process_upsert_queue_hierarchy_aware(processor_type="persistent"),
                        name="upsert_data"
                    )

        # Wait for all queues to complete
        await _wait_for_queue_completion(q_container, project_key, my_logger)

        return True

    except *(TypeError, Exception) as exc_group:
        my_logger.error(f"Error in producer-consumer processing: {exc_group}")
        for exc in exc_group.exceptions:
            my_logger.error(f"Individual exception: {exc}")



async def _wait_for_queue_completion(
        q_container: DynamicContainer,
        project_key: str,
        my_logger: Logger
):
    """Wait for all queues to complete processing."""
    asyncio.current_task().set_name(f"wait_for_queue_completion_{project_key}")
    my_logger.debug(f"Waiting for queue completion for {project_key}")

    # Log queue sizes before waiting
    queue_sizes = {
        name: queue.qsize()
        for name, queue in q_container.queue_selector().items()
    }
    my_logger.debug(f"Queue sizes before join: {queue_sizes}")

    active_tasks = [task for task in asyncio.all_tasks() if not task.done()]
    my_logger.debug(f"Active tasks: {len(active_tasks)}")

    for i, task in enumerate(active_tasks):
        my_logger.debug(f"Task {i}: {task}")
        # Get stack trace of hanging task
        task.print_stack()

    # Wait for all queues to complete
    await asyncio.gather(
        *(queue_name.join() for queue_name in q_container.queue_selector().values())
    )

    # Log final queue sizes
    final_sizes = {
        name: queue.qsize()
        for name, queue in q_container.queue_selector().items()
    }
    my_logger.debug(f"Final queue sizes: {final_sizes}")


async def _cleanup_orphaned_parent_references(pg_session, my_logger: Logger):
    """Clean up orphaned parent references in issues."""
    asyncio.current_task().set_name("cleanup_orphaned_parent_references")
    my_logger.debug("Cleaning up orphaned parent references")

    # Find parent_keys that don't have corresponding issue records
    issue_parent = aliased(Issue)
    orphaned_parents_subquery = (
        select(issue_parent.parent_key)
        .select_from(issue_parent)
        .outerjoin(Issue, issue_parent.parent_key == Issue.key)
        .filter(
            Issue.key.is_(None),
            issue_parent.parent_key.is_not(None)
        )
        .distinct()
    ).subquery()

    # Update issues to remove orphaned parent references
    update_stmt = (
        update(Issue)
        .where(Issue.parent_key.in_(select(orphaned_parents_subquery.c.parent_key)))
        .values(parent_key=null(), parent_id=null())
        .execution_options(synchronize_session='fetch')
    )

    result = await pg_session.execute(update_stmt)
    affected_rows = result.rowcount

    my_logger.info(f"Cleaned up {affected_rows} orphaned parent references")


async def _update_run_details(
        pg_session,
        process_start_marker: datetime,
        my_logger: Logger
):
    """Update run details after successful processing."""
    asyncio.current_task().set_name("update_run_details")
    try:
        run_details = RunDetailsJira(topic="Issue", last_run=process_start_marker)
        await pg_session.merge(run_details)
        await pg_session.commit()
        my_logger.info("Run details updated successfully")

    except Exception as e:
        my_logger.error(f"Failed to update run details: {str(e)}")
        raise


# Define a type alias for clarity
# QueuePair = tuple[Queue, str]
# QueueList = list[QueuePair]


@inject
def create_db_extension(
        db_rw=Provide[DatabaseSessionManagerContainer.database_rw]
) -> str:
    return_value = "DB EXISTS"
    print(f"type : {type(db_rw)}")
    try:
        # with Database(schema='public').session() as pg_session:
        with db_rw.session() as pg_session:
            if not database_exists(pg_session.bind.url):
                create_database(pg_session.bind.url)
                return_value = f"DB CREATED"

            # Create extensions
            for value in [
                "citext", "ltree", "intarray", "hstore", "btree_gist", "pg_trgm"
            ]:
                pg_session.execute(
                    text(f"CREATE EXTENSION IF NOT EXISTS {value} WITH SCHEMA pg_catalog;")
                )
                print(f"extension {value} created")
    except Exception as e:
        return_value = "EXCEPTION"
        handle_exception(e)

    return return_value

def iso_ts(date):
    """Convert date to ISO UTC timestamp string"""
    return f"{date.isoformat()}T00:00:00+00:00"

def get_partition_ddl_statements():
    today = datetime.now(timezone.utc).date()
    ddls = []

    # Monthly partitions — task_executions, task_metrics
    for i in range(3):
        start = (today.replace(day=1) + timedelta(days=32 * i)).replace(day=1)
        end = (start.replace(day=28) + timedelta(days=4)).replace(day=1)
        suffix = f"{start.year}_{start.month:02d}"

        for table in ["task_execution", "task_metrics"]:
            ddl = DDL(f"""
                CREATE TABLE IF NOT EXISTS public.{table}_{suffix}
                PARTITION OF public.{table}
                FOR VALUES FROM ('{iso_ts(start)}') TO ('{iso_ts(end)}');
            """)
            ddls.append((table, ddl))

    # Weekly partitions — task_state_changes, task_await_events
    for i in range(3):
        start = today + timedelta(days=(7 * i - today.weekday()))
        end = start + timedelta(days=7)
        year, week = start.isocalendar()[:2]
        suffix = f"{year}_w{week:02d}"

        for table in ["task_state_change", "task_await_event"]:
            ddl = DDL(f"""
                CREATE TABLE IF NOT EXISTS public.{table}_{suffix}
                PARTITION OF public.{table}
                FOR VALUES FROM ('{iso_ts(start)}') TO ('{iso_ts(end)}');
            """)
            ddls.append((table, ddl))

    # Daily partitions — task_stack_snapshot
    for i in range(3):
        start = today + timedelta(days=i)
        end = start + timedelta(days=1)
        suffix = f"{start.year}_{start.month:02d}_{start.day:02d}"

        ddl = DDL(f"""
            CREATE TABLE IF NOT EXISTS public.task_stack_snapshot_{suffix}
            PARTITION OF public.task_stack_snapshot
            FOR VALUES FROM ('{iso_ts(start)}') TO ('{iso_ts(end)}');
        """)
        ddls.append(("task_stack_snapshot", ddl))

    # Default partitions (for safety)
    default_partitions = {
        "task_execution": "task_execution_default",
        "task_metrics": "task_metrics_default",
        "task_state_change": "task_state_change_default",
        "task_await_event": "task_await_event_default",
        "task_stack_snapshot": "task_stack_snapshot_default",
    }

    for table, default_name in default_partitions.items():
        ddl = DDL(f"""
            CREATE TABLE IF NOT EXISTS public.{default_name}
            PARTITION OF public.{table} DEFAULT;
        """)
        ddls.append((table, ddl))

    return ddls

def attach_partition_ddls(session):
    """Create partition tables using DDL statements with proper execution"""
    from dags.data_pipeline.database.get_model_by_name import get_model_by_name

    table_dict = {
        'task_execution': 'TaskExecution',
        'task_state_change': 'TaskStateChange',
        'task_await_event': 'TaskAwaitEvent',
        'task_stack_snapshot': 'TaskStackSnapshot',
        'task_metrics': 'TaskMetrics',
        'log_entry': 'LogEntry'
    }

    # Get current database session from context
    from sqlalchemy import create_engine


    try:

        # Execute DDL statements for each partition
        for table_name, ddl in get_partition_ddl_statements():
            if table_name in table_dict:
                try:
                    # Get the model class to ensure it exists
                    model_class = get_model_by_name(table_dict[table_name])

                    # Execute the DDL statement
                    session.execute(ddl)
                    session.commit()
                    print(f"Created partition for {table_name}: {ddl}")

                except Exception as e:
                    print(f"Error creating partition for {table_name}: {e}")
                    continue

    except Exception as e:
        print(f"Error in attach_partition_ddls: {e}")
        # print(f"table_name = {table_name}, {get_model_by_name(table_dict[table_name])}")
        try:
            event.listen(
                # Base.metadata.tables[f"public.{table_name}"],
                get_model_by_name(table_dict[table_name]).__table__,
                "after_create",
                ddl
            )
        except KeyError:
            # fallback for unqualified table names
            event.listen(
                Base.metadata.tables[table_name],
                "after_create",
                ddl
            )
def create_schema_tables_ddl(
    schema_name: str,
    db_rw,
    db_ro,
    my_logger: Logger|None = None
        # db_rw=Provide[DatabaseSessionManagerContainer.database_rw],
        # db_ro=Provide[DatabaseSessionManagerContainer.database_ro],
        # my_logger: Logger = Provide[LoggerContainer.logger]
) -> str:
    function_status = False
    try:
        with db_rw.session() as pg_session:
            with pg_session.begin():
                # with Database(schema=schema_name).session() as pg_session:
                # Apply the function and trigger to each schema
                engine = pg_session.bind
                inspector = inspect(engine)

                # if not pg_session.bind.dialect.has_schema(pg_session.bind, schema_name):
                if schema_name not in inspector.get_schema_names():
                    pg_session.execute(CreateSchema(schema_name, if_not_exists=True))

                # Create users if they don't exist
                rw_password = generate_password()
                ro_password = generate_password()

                rw_password_md5 = md5_hash(rw_password, f"{schema_name}_rw")
                ro_password_md5 = md5_hash(ro_password, f"{schema_name}_ro")

                # Create roles safely
                # if using md5, then instead of rw_password pass rw_password_md5.
                # for clarity :rw_password can be relabelled as :rw_password_md5
                pg_session.execute(text("""
                    DO $$ 
                    BEGIN 
                        IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = :rw_role_name) THEN 
                            EXECUTE format('CREATE ROLE %I LOGIN PASSWORD %L', :rw_role_name, :rw_password); 
                        END IF; 
                    END $$;
                """), {"rw_role_name": f"{schema_name}_rw", "rw_password": rw_password})

                pg_session.execute(text("""
                    DO $$ 
                    DECLARE
                        ro_role TEXT := :ro_role_name;
                        ro_pass TEXT := :ro_password;
                    BEGIN 
                        IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = ro_role) THEN 
                            EXECUTE format('CREATE ROLE %I LOGIN PASSWORD %L', ro_role, ro_pass); 
                        END IF; 
                    END $$;
                """), {"ro_role_name": f"{schema_name}_ro", "ro_password": ro_password})

                # Set default privileges for tables, functions, and sequences
                pg_session.execute(
                    text(
                        f"""
                            ALTER DEFAULT PRIVILEGES IN SCHEMA {schema_name}
                            GRANT ALL PRIVILEGES ON TABLES TO {schema_name}_rw
                        """
                    )
                )

                pg_session.execute(
                    text(
                        f"""
                            ALTER DEFAULT PRIVILEGES IN SCHEMA {schema_name}
                            GRANT SELECT ON TABLES TO {schema_name}_ro
                        """
                    )
                )

                pg_session.execute(
                    text(
                        f"""
                            ALTER DEFAULT PRIVILEGES IN SCHEMA {schema_name}
                            GRANT EXECUTE ON FUNCTIONS TO {schema_name}_rw
                        """
                    )
                )

                pg_session.execute(
                    text(
                        f"""
                            ALTER DEFAULT PRIVILEGES IN SCHEMA {schema_name}
                            GRANT  USAGE, SELECT ON SEQUENCES TO {schema_name}_rw
                        """
                    )
                )

                # Grant permissions on schema and existing objects
                pg_session.execute(
                    text(f"GRANT ALL PRIVILEGES ON SCHEMA {schema_name} TO {schema_name}_rw")
                )

                pg_session.execute(
                    text(f"GRANT SELECT ON ALL TABLES IN SCHEMA {schema_name} TO {schema_name}_ro")
                )

                pg_session.execute(
                    text(f"GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA {schema_name} TO {schema_name}_rw")
                )

                pg_session.execute(
                    text(f"GRANT USAGE, SELECT ON ALL SEQUENCES  IN SCHEMA {schema_name} TO {schema_name}_rw")
                )

                pg_session.execute(
                    text(f"GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA {schema_name} TO {schema_name}_rw")
                )

                # Grant select permission on public
                pg_session.execute(
                    text(f"GRANT SELECT ON ALL TABLES IN SCHEMA public TO {schema_name}_rw")
                )
                pg_session.execute(
                    text(f"GRANT SELECT ON ALL TABLES IN SCHEMA public TO {schema_name}_ro")
                )
            pg_session.commit()

            # Access the engine via session.bind
            engine_rw = pg_session.bind.engine
            with db_ro.session() as pg_ro:
                engine_ro = pg_ro.bind.engine

            # Add entries to KeePass
            kp = PyKeePass(filename=f"{os.getenv('AIRFLOW_HOME')}/Database.kdbx",
                           keyfile=f"{os.getenv('AIRFLOW_HOME')}/Database.key")

            group = kp.find_groups(name='DB', first=True)

            if not kp.find_entries(title=f"{schema_name}_rw", first=True):
                kp.add_entry(
                    group, title=f"{schema_name}_rw", username=f"{schema_name}_rw", password=f"{rw_password}"
                )
                entry = kp.find_entries(title=f"{schema_name}_rw", first=True)
                for key, value in [
                    ('DB_NAME', engine_rw.url.database),
                    ('DB_SERVER_NAME', engine_rw.url.host),
                    ('DB_SERVER_RO_PORT', str(engine_ro.url.port)),
                    ('DB_SERVER_RW_PORT', str(engine_rw.url.port)),
                ]:
                    entry.set_custom_property(key=key, value=value)
                print(f"password {rw_password} set for username={schema_name}_rw")
            else:
                my_logger.info(f"entry with {schema_name}_rw exits")

            if not kp.find_entries(title=f"{schema_name}_ro", first=True):
                kp.add_entry(
                    group, title=f"{schema_name}_ro", username=f"{schema_name}_ro", password=f"{ro_password}"
                )
                entry = kp.find_entries(title=f"{schema_name}_ro", first=True)
                for key, value in [
                    ('DB_NAME', engine_rw.url.database),
                    ('DB_SERVER_NAME', engine_rw.url.host),
                    ('DB_SERVER_RO_PORT', str(engine_ro.url.port)),
                    ('DB_SERVER_RW_PORT', str(engine_rw.url.port)),
                ]:
                    entry.set_custom_property(key=key, value=value)
                print(f"password {ro_password} set for username={schema_name}_ro")
            else:
                my_logger.info(f"entry with {schema_name}_ro exits")

            kp.save()
            # Due to performance issue, migrated tscv_summary_description to Generated column.
            # Retain this for reference.
            # # Define the DDL for creating the function
            # function_issue_tsv_vector = DDL(
            #     f"""
            #     DO
            #     $do$
            #     BEGIN
            #         IF NOT EXISTS (
            #             SELECT 1 FROM pg_proc
            #             WHERE proname = 'update_tsv_combined'
            #         ) THEN
            #             CREATE OR REPLACE FUNCTION update_tsv_combined() RETURNS trigger AS $$
            #             BEGIN
            #                 NEW.tscv_summary_description :=
            #                     setweight(to_tsvector('english', coalesce(NEW.summary, '')), 'A') ||
            #                     setweight(to_tsvector('english', coalesce(NEW.description_markdown, '')), 'B');
            #                 RETURN NEW;
            #             END
            #             $$ LANGUAGE plpgsql;
            #         END IF;
            #     END $do$;
            #     """
            # )
            # # Listen for the 'after_create' event to create the function
            # event.listen(
            #     Issue.__table__,
            #     'after_create',
            #     function_issue_tsv_vector.execute_if(dialect='postgresql')
            # )
            #
            # trigger_issue_tsv_summary_description = DDL(
            #     f"""
            #     DO $do$
            #     BEGIN
            #         IF NOT EXISTS (
            #             SELECT 1 FROM pg_trigger
            #             WHERE tgname = 'tsv_combined_update'
            #             AND tgrelid = (
            #             SELECT oid FROM pg_class WHERE relname = 'issue' AND relnamespace = (
            #             SELECT oid FROM pg_namespace WHERE nspname = '{schema_name}')
            #             )
            #         ) THEN
            #             CREATE TRIGGER tsv_combined_update
            #             BEFORE INSERT OR UPDATE ON {schema_name}.issue
            #             FOR EACH ROW EXECUTE FUNCTION update_tsv_combined();
            #         END IF;
            #     END $do$;
            #     """
            # )
            #
            # # Listen for the 'after_create' event to create the trigger, passing the schema dynamically
            # event.listen(
            #     Issue.__table__,
            #     'after_create',
            #     trigger_issue_tsv_summary_description.execute_if(dialect=("postgresql",))
            # )

            # Create log_entry table with monthly partitioning

            # Create the main partitioned table
            log_table_ddl = DDL(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.log_entry (
                    id SERIAL,
                    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                    level VARCHAR(20) NOT NULL,
                    logger_name VARCHAR(100) NOT NULL,
                    module VARCHAR(200),
                    function_name VARCHAR(100),
                    line_number INTEGER,
                    message TEXT NOT NULL,
                    correlation_id VARCHAR(50),
                    task_id VARCHAR(50),
                    coroutine_name VARCHAR(100),
                    thread_id INTEGER,
                    process_id INTEGER,
                    hostname VARCHAR(100),
                    user_id VARCHAR(100),
                    session_id VARCHAR(100),
                    request_id VARCHAR(100),
                    extra_data JSONB,
                    stack_trace TEXT,
                    performance_data JSONB,
                    PRIMARY KEY (id, timestamp)
                ) PARTITION BY RANGE (timestamp);

                -- Create indexes on main table
                CREATE INDEX IF NOT EXISTS idx_log_entry_timestamp ON {schema_name}.log_entry (timestamp);
                CREATE INDEX IF NOT EXISTS idx_log_entry_level ON {schema_name}.log_entry (level);
                CREATE INDEX IF NOT EXISTS idx_log_entry_correlation_id ON {schema_name}.log_entry (correlation_id);
                CREATE INDEX IF NOT EXISTS idx_log_entry_logger_name ON {schema_name}.log_entry (logger_name);
                CREATE INDEX IF NOT EXISTS idx_log_entry_module_function ON {schema_name}.log_entry (module, function_name);
                CREATE INDEX IF NOT EXISTS idx_log_entry_task_id ON {schema_name}.log_entry (task_id);
                CREATE INDEX IF NOT EXISTS idx_log_entry_timestamp_level ON {schema_name}.log_entry (timestamp, level);
                CREATE INDEX IF NOT EXISTS idx_log_entry_correlation_timestamp ON {schema_name}.log_entry (correlation_id, timestamp);
            """)

            pg_session.execute(log_table_ddl)

            # Create partitions for current month and next 3 months
            current_date = datetime.now()
            for i in range(4):
                partition_date = current_date + timedelta(days=30 * i)
                year = partition_date.year
                month = partition_date.month

                partition_ddl = DDL(log_entry.LogEntry.get_partition_ddl(schema_name, year, month))
                pg_session.execute(partition_ddl)

            my_logger.info(f"Created log_entry table with monthly partitioning in schema public")

            pg_session.bind.update_execution_options(schema_translate_map={None: schema_name})

            Base.metadata.create_all(bind=pg_session.bind, checkfirst=True)
            pg_session.commit()
            attach_partition_ddls(pg_session)

            my_logger.debug(f"Tables created: {Base.metadata.tables.keys()}")
            function_status = True
    except Exception as e:
        print(f"exception {e}")
        handle_exception(e)

    return "SUCCESS" if function_status else "FAILED"


async def add_comment(
        issue_key: str,
        my_logger: Logger,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details]
):
    # config_dict = get_env_variables()
    base_url = jira_entry.url
    endpoint = f"/rest/api/3/issue/{issue_key}"
    # config_dict['url_seg'] = f"/rest/api/3/issue/{issue_key}"
    payload = {'fields': ['issuekey', 'assignee', 'status', 'reporter']}

    url_issue = f"{base_url}{endpoint}"
    url_issue_comment = f"{base_url}{endpoint}/comment"
    timeout = aiohttp.ClientTimeout(total=300)
    connector = aiohttp.TCPConnector(
        limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
        ssl=ssl.create_default_context()
    )
    async with aiohttp.ClientSession(
        headers=jira_entry.custom_properties,        timeout=timeout,
        connector=connector, raise_for_status=True
    ) as http_session:
        response = await fetch_with_retries_get(http_session, url_issue, params=payload)

        if not response.get('success'):
            my_logger.error(f"Failed to fetch issue details: {response.get('exception')}")
            return

        result = response.get('result', {})
        issue_id = result.get('id')
        fields = result.get('fields', {})
        assignee = fields.get('assignee')
        reporter = fields.get('reporter')
        status = fields.get('status')

        if assignee:
            print(assignee.get('accountId', None), assignee.get('emailAddress', None),
                  assignee.get('displayName', None))
        if status:
            print(status.get('id'), status.get('name'), status.get('statusCategory', {}).get('name'))
        if reporter:
            print(reporter.get('accountId'), reporter.get('emailAddress'), reporter.get('displayName'))

        params_comments = {
            "startAt": 0,
            "maxResults": 5000
        }

        response = await fetch_with_retries_get(http_session, url_issue_comment, params=params_comments)
        rprint(response)

        dictionary = {
            "body": {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "mention",
                                "attrs": {
                                    "id": reporter.get('accountId'),
                                    "text": reporter.get('displayName'),
                                    "accessLevel": ""
                                }
                            },
                            {
                                "type": "text",
                                "text": "This is test comment 11/8/2024",
                            },
                            {
                                "type": "hardBreak"
                            },
                        ]
                    }
                ]
            }
        }

        response = await fetch_with_retries_post(
            http_session, url=url_issue_comment,
            json_payload=dictionary
        )
        print(response)


# Section for defining ContextVar
commit_transaction: bool = True
lock = asyncio.Lock()
# Define a global or class-level lock
issue_links_lock = asyncio.Lock()
issue_worklog_lock = asyncio.Lock()
issue_changelog_lock = asyncio.Lock()
issue_comment_lock = asyncio.Lock()

data_load: ContextVar[str] = ContextVar("data_load", default="initial")



# Task done callback
@inject
def helper_done_callback(task, my_logger: Logger = Provide[LoggerContainer.logger]):
    if task.exception():
        try:
            task.result()
        except CancelledError as e:
            my_logger.warning(f"{task} is cancelled")
        except Exception as e:
            my_logger.exception(f'Task {task} failed with exception {e}')
            raise e


def create_task_helper(coroutine, name: str | None = None, context_param=None):
    task = asyncio.create_task(coroutine, name=name, context=context_param)
    task.add_done_callback(helper_done_callback)
    return task


def create_task_group_helper(coroutine, task_group, name: str | None = None, context_param=None):
    task = task_group.create_task(coroutine, name=name, context=context_param)
    task.add_done_callback(helper_done_callback)

    return task



def create_layout():
    layout = Layout(name="root")
    width, height = shutil.get_terminal_size()

    if width > 120 and height > 30:
        # Large screens with ample space

        layout.split_row(
            Layout(name="left", ratio=2),
            Layout(name="right", ratio=3)
        )
        layout["left"].split_column(
            Layout(name="left-first", size=height // 6),
            Layout(name="left-second", size=height // 3),
            Layout(name="left-third", ratio=1),
        )
        layout["right"].split_column(
            Layout(name="right-top", size=height // 2),
            Layout(name="right-bottom", ratio=1),
        )
    elif 80 < width <= 120 or 20 < height <= 30:  # Medium-sized screens
        layout.split_column(
            Layout(name="main", ratio=1),
        )
        layout["main"].split_row(
            Layout(name="left", ratio=2),
            Layout(name="right", ratio=3)
        )
        layout["left"].split_column(
            Layout(name="left-first", size=height // 8),
            Layout(name="left-second", size=height // 4),
            Layout(name="left-third", ratio=1),
        )
        layout["right"].split_column(
            Layout(name="right-top", size=height // 3),
            Layout(name="right-bottom", ratio=1),
        )
    else:  # Small screens
        layout.split_column(
            Layout(name="left", ratio=2),
            Layout(name="right", ratio=3),
        )
        layout["left"].split_column(
            Layout(name="left-first", size=3),
            Layout(name="left-second", ratio=4),
            Layout(name="left-third", size=3),
        )
        layout["right"].split_column(
            Layout(name="right-top", ratio=3),
            Layout(name="right-bottom", ratio=2),
        )

    return layout


@dataclass
class TaskInfo:
    function: Callable
    args: List[Any]
    kwargs: dict
    description: str = ""

    def __post_init__(self):
        if not self.description:
            self.description = self.function.__name__


@dataclass
class SystemMetrics:
    """System metrics data structure"""
    memory_usage_mb: float = 0.0
    memory_percent: float = 0.0
    cpu_percent: float = 0.0
    asyncio_tasks_count: int = 0
    http_requests_count: int = 0
    tcp_connections_count: int = 0
    retry_count: int = 0
    failure_count: int = 0
    timestamp: float = 0.0

    def __post_init__(self):
        if self.timestamp == 0:
            self.timestamp = time.time()


class SystemMetricsCollector:
    """Collects system parameters for monitoring"""

    def __init__(self):
        self.http_request_counter = 0
        self.retry_counter = 0
        self.failure_counter = 0
        self.tcp_connector = None

    def increment_http_requests(self):
        """Increment HTTP request counter"""
        self.http_request_counter += 1

    def increment_retries(self):
        """Increment retry counter"""
        self.retry_counter += 1

    def increment_failures(self):
        """Increment failure counter"""
        self.failure_counter += 1

    def set_tcp_connector(self, connector):
        """Set TCP connector for monitoring"""
        self.tcp_connector = connector

    def collect_metrics(self) -> SystemMetrics:
        """Collect current system metrics"""
        try:
            # Memory usage
            memory_info = psutil.virtual_memory()
            memory_usage_mb = memory_info.used / (1024 * 1024)
            memory_percent = memory_info.percent

            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)

            # Asyncio tasks count
            try:
                asyncio_tasks_count = len(asyncio.all_tasks())
            except RuntimeError:
                asyncio_tasks_count = 0

            # TCP connections (if connector available)
            tcp_connections_count = 0
            if self.tcp_connector and hasattr(self.tcp_connector, '_conns'):
                try:
                    tcp_connections_count = len(self.tcp_connector._conns)
                except (AttributeError, TypeError):
                    tcp_connections_count = 0

            return SystemMetrics(
                memory_usage_mb=memory_usage_mb,
                memory_percent=memory_percent,
                cpu_percent=cpu_percent,
                asyncio_tasks_count=asyncio_tasks_count,
                http_requests_count=self.http_request_counter,
                tcp_connections_count=tcp_connections_count,
                retry_count=self.retry_counter,
                failure_count=self.failure_counter,
                timestamp=time.time()
            )
        except Exception as e:
            # Return default metrics if collection fails
            return SystemMetrics(
                timestamp=time.time()
            )


@dataclass
class AsyncProcessInfo:
    """Information about an async process"""
    name: str
    process_type: str  # 'producer', 'consumer', 'task', 'consumer_task'
    schema: str
    status: str = "not_started"  # not_started, running, completed, failed
    start_time: float = 0.0
    records_processed: int = 0
    last_activity: float = 0.0
    pre_populated: bool = False  # Track if this was pre-populated

    def __post_init__(self):
        if self.start_time == 0 and not self.pre_populated:
            self.start_time = time.time()
        if self.last_activity == 0 and not self.pre_populated:
            self.last_activity = time.time()


class AsyncProcessTracker:
    """Tracks async processes for monitoring"""

    def __init__(self):
        self.processes: Dict[str, AsyncProcessInfo] = {}
        self.rotation_index = 0
        self.prepopulate_processes()

    def prepopulate_processes(self):
        """Prepopulate tracker with known processes"""
        # Define process names we want to monitor
        self.monitored_process_names = {
            "get_all_jira_boards": {"type": "task", "schema": "system"},
            "changelog_processor": {"type": "consumer_task", "schema": "changelog"},
            "worklog_processor": {"type": "consumer_task", "schema": "worklog"}, 
            "comment_processor": {"type": "consumer_task", "schema": "comment"},
            "issue_links_processor": {"type": "consumer_task", "schema": "issue_links"},
            "issue_processor": {"type": "consumer_task", "schema": "issue"},
            "get_sprint_details": {"type": "task", "schema": "dynamic"},
            "upsert_issue_classification": {"type": "task", "schema": "dynamic"}
        }

        # Pre-populate with not_running status
        for name, info in self.monitored_process_names.items():
            if name not in self.processes:
                self.processes[name] = AsyncProcessInfo(
                    name=name,
                    process_type=info["type"],
                    schema=info["schema"],
                    status="not_running",
                    pre_populated=True
                )

    def register_process(self, name: str, process_type: str, schema: str):
        """Register a new async process or update existing one"""
        if name in self.processes:
            # Update existing process (likely pre-populated)
            process = self.processes[name]
            process.status = "running"
            process.start_time = time.time()
            process.last_activity = time.time()
            process.pre_populated = False
        else:
            # Create new process
            self.processes[name] = AsyncProcessInfo(
                name=name,
                process_type=process_type,
                schema=schema,
                status="running"
            )

    def update_process(self, name: str, status: str = None, records_processed: int = None):
        """Update process information"""
        if name in self.processes:
            process = self.processes[name]
            if status:
                process.status = status
            if records_processed is not None:
                process.records_processed = records_processed
            process.last_activity = time.time()

    def get_processes_by_type(self, process_type: str) -> List[AsyncProcessInfo]:
        """Get processes by type"""
        return [p for p in self.processes.values() if p.process_type == process_type]

    def get_rotated_processes(self, max_display: int = 5) -> List[AsyncProcessInfo]:
        """Get processes for rotated display, including not_running processes"""
        # First, sync with actual asyncio tasks
        self.sync_with_asyncio_tasks()
        
        # Include all processes except completed ones
        # Sort by priority: running first, then not_running, then failed
        all_processes = list(self.processes.values())
        
        # Filter out completed processes
        active_processes = [p for p in all_processes if p.status not in ["completed", "not_running"]]
        
        # Sort by status priority (running first, then not_running, then failed)
        status_priority = {"running": 1, "not_running": 2, "failed": 3}
        active_processes.sort(key=lambda p: status_priority.get(p.status, 4))

        if len(active_processes) <= max_display:
            return active_processes

        # Rotate through active processes
        start_idx = self.rotation_index % len(active_processes)
        end_idx = min(start_idx + max_display, len(active_processes))

        result = active_processes[start_idx:end_idx]
        if len(result) < max_display and start_idx > 0:
            # Wrap around if needed
            remaining = max_display - len(result)
            result.extend(active_processes[:remaining])

        self.rotation_index = (self.rotation_index + max_display) % len(active_processes)
        return result

    def get_active_processes_count(self) -> int:
        """Get count of active (non-completed) processes"""
        return len([p for p in self.processes.values() if p.status != "completed"])

    def sync_with_asyncio_tasks(self):
        """Sync tracker with actual asyncio tasks"""
        try:
            # Get all current asyncio tasks
            current_tasks = asyncio.all_tasks()
            
            # Get tasks that match our monitored process names
            running_task_names = set()
            for task in current_tasks:
                task_name = task.get_name()
                matched_process_name = None
                
                # Check for exact match first
                if task_name in self.monitored_process_names:
                    matched_process_name = task_name
                    running_task_names.add(task_name)
                else:
                    # Check for partial matches (for dynamic task names)
                    for process_name in self.monitored_process_names:
                        if process_name in task_name:
                            matched_process_name = process_name
                            running_task_names.add(process_name)
                            break
                
                if matched_process_name:
                    # Update the process info based on task state
                    if matched_process_name in self.processes:
                        process = self.processes[matched_process_name]
                        
                        # Map asyncio task state to our status
                        if hasattr(task, '_state'):
                            task_state = task._state
                            if task_state == 'PENDING':
                                process.status = "running"
                            elif task_state == 'FINISHED':
                                process.status = "completed"
                            elif task_state == 'CANCELLED':
                                process.status = "failed"
                        else:
                            # Fallback: if task exists and is not done, it's running
                            if not task.done():
                                process.status = "running"
                            elif task.cancelled():
                                process.status = "failed"
                            elif task.exception():
                                process.status = "failed"
                            else:
                                process.status = "completed"
                        
                        process.last_activity = time.time()
                        
                        # Set start time if not already set
                        if process.start_time == 0.0:
                            process.start_time = time.time()
            
            # Update processes that are not currently running
            for name, process in self.processes.items():
                if name not in running_task_names and process.status == "running":
                    # Task is no longer running, mark as completed if it was running
                    process.status = "completed"
                    
        except Exception as e:
            # Silently handle errors to avoid breaking the monitoring
            pass


@dataclass
class ProcessingStats:
    total_records_processed: int = 0
    start_time: float = 0
    last_update_time: float = 0
    processing_rates: List[float] = None

    def __post_init__(self):
        if self.processing_rates is None:
            self.processing_rates = []
        if self.start_time == 0:
            self.start_time = time.time()
        if self.last_update_time == 0:
            self.last_update_time = time.time()


class ManagedProgress(Progress):
    """Custom Progress class that automatically manages completed tasks"""

    def __init__(self, *args, max_completed_visible: int = 4, **kwargs):
        super().__init__(*args, **kwargs)
        self.max_completed_visible = max_completed_visible
        self.completed_tasks = []  # Track completed tasks in order
        self.hidden_tasks = set()  # Track hidden task IDs

    def mark_completed(self, task_id: TaskID, return_value: str = "Completed"):
        """Mark a task as completed and manage visibility"""
        # Update the task
        self.update(task_id, advance=100, return_value=return_value)
        self.stop_task(task_id)

        # Track as completed
        if task_id not in self.completed_tasks:
            self.completed_tasks.append(task_id)

        # Hide oldest completed tasks if needed
        if len(self.completed_tasks) > self.max_completed_visible:
            tasks_to_hide = self.completed_tasks[:-self.max_completed_visible]

            for old_task_id in tasks_to_hide:
                if old_task_id not in self.hidden_tasks:
                    self._hide_task(old_task_id)
                    self.hidden_tasks.add(old_task_id)

            # Remove hidden tasks from completed list
            self.completed_tasks = [t for t in self.completed_tasks if t not in self.hidden_tasks]

    def _hide_task(self, task_id: TaskID):
        """Hide a completed task"""
        try:
            # Method 1: Try to remove the task entirely
            if hasattr(self, '_tasks') and task_id in self._tasks:
                del self._tasks[task_id]
        except:
            try:
                # Method 2: Make it invisible by setting description to empty
                self.update(task_id, description="", return_value="")
                # Reset progress to 0 to minimize space
                task = self._tasks.get(task_id)
                if task:
                    task.total = 0
                    task.completed = 0
            except:
                pass

    def get_visible_tasks(self):
        """Get only visible (non-hidden) tasks"""
        if hasattr(self, '_tasks'):
            return {tid: task for tid, task in self._tasks.items() if tid not in self.hidden_tasks}
        return {}

    def cleanup_hidden_tasks(self):
        """Remove all hidden tasks"""
        for task_id in list(self.hidden_tasks):
            try:
                if hasattr(self, '_tasks') and task_id in self._tasks:
                    del self._tasks[task_id]
            except:
                pass
        self.hidden_tasks.clear()

class RichDisplayManager:
    """Manages Rich UI components and ensures single Live instance"""

    def __init__(self):
        self.console = Console()
        self.live: Optional[Live] = None
        self.layout: Optional[Layout] = None
        self._is_active = False

    def create_layout(self) -> Layout:
        """Create the main layout structure"""
        layout = Layout()
        layout.split_row(
            Layout(name="left", ratio=4),
            Layout(name="right", ratio=5)
        )

        layout["left"].split_column(
            Layout(name="overall", ratio=2),
            Layout(name="queues", ratio=5),
            Layout(name="stats_progress", ratio=2),
            Layout(name="stats_table", ratio=6)
        )

        layout["right"].split_column(
            Layout(name="tasks", ratio=1),
            Layout(name="monitor", ratio=1),
        )
        self.layout = layout
        return layout

    @asynccontextmanager
    async def managed_live(self, layout):
        """Context manager for Rich Live to ensure single instance"""
        asyncio.current_task().set_name("managed_live")
        if self._is_active:
            raise RuntimeError("Rich Live is already active")

        self._is_active = True
        self.live = Live(layout, auto_refresh=True, screen=False, transient=False)

        try:
            with self.live:
                yield self.live
        finally:
            self._is_active = False
            self.live = None

    def update_layout_section(self, section: str, content):
        """Update a specific section of the layout"""
        if self.layout and section in self.layout:
            self.layout[section].update(content)


class ProgressManager:
    """Manages all progress bars and statistics"""

    def __init__(self):
        self.overall_progress = None
        self.job_progress = None
        self.stats_progress = None
        self.stats_table = None
        self.queue_status_table = None
        self.processing_stats = ProcessingStats()
        self.completed_tasks = []  # Track completed task IDs in order
        self.max_visible_completed = 4  # Maximum number of completed tasks to show

        # Enhanced monitoring components - use global instances
        self.system_metrics_collector = global_system_metrics_collector
        self.async_process_tracker = global_async_process_tracker

    def initialize_progress_bars(self, total_tasks: int):
        """Initialize all progress bar components"""
        self.overall_progress = Progress(
            "{task.description}",
            MofNCompleteColumn(separator="/")
        )

        # Use the custom ManagedProgress for job progress
        self.job_progress = ManagedProgress(
            "{task.description}",
            TextColumn("[bold]Params:[/bold] {task.fields[parameters]}", justify="left"),
            SpinnerColumn(),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            " ",
            TimeElapsedColumn(),
            TextColumn("{task.fields[return_value]}", justify="left"),
            max_completed_visible = 3
        )

        self.stats_progress = Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            "[progress.percentage]{task.percentage:>3.0f}%",
            "•",
            TextColumn("Processed: {task.completed} / {task.total}"),
            TimeElapsedColumn(),
        )

        # Initialize overall task
        self.overall_task_id = self.overall_progress.add_task("Overall Task Progress", total=total_tasks)

        # Initialize stats task
        self.stats_task_id = self.stats_progress.add_task(
            description="Processing Records", total=None, start=False
        )

    def initialize_stats_table(self):
        """Initialize the statistics table"""
        self.stats_table = Table(
            show_footer=False, show_header=True, title="Processing Statistics"
        )
        self.stats_table.add_column("Metric", justify="left", style="cyan", no_wrap=True)
        self.stats_table.add_column("Value", justify="right", style="green")
        self.stats_table.add_column("Unit", justify="left", style="dim")

        # Initialize with default values
        stats_data = [
            ("Records Processed", "0", "records"),
            ("Total Time", "0.0", "seconds"),
            ("Current TPS", "0.0", "records/sec"),
            ("Average TPS", "0.0", "records/sec"),
            ("Completion", "0.0", "%")
        ]

        for metric, value, unit in stats_data:
            self.stats_table.add_row(metric, value, unit)

    def initialize_queue_table(self, schema: str, q_container):
        """Initialize the queue status table"""
        self.queue_status_table = Table(
            show_footer=False, show_header=False
        )
        self.queue_status_table.pad_edge = False
        self.queue_status_table.add_column("Queue Name", justify="left", style="cyan", no_wrap=True)
        self.queue_status_table.add_column("qsize", style="magenta", justify="right")
        self.queue_status_table.add_column("Queue Name", justify="left", style="cyan", no_wrap=True)
        self.queue_status_table.add_column("qsize", style="magenta", justify="right")

        # Initialize table with current queue data
        q_container.config.override({"schema_name": schema})
        queue_items = list(q_container.queue_selector().items())

        for i in range(0, len(queue_items), 2):
            q1_name, q1_queue = queue_items[i]
            q1_size = str(q1_queue.qsize()).zfill(4)

            if i + 1 < len(queue_items):
                q2_name, q2_queue = queue_items[i + 1]
                q2_size = str(q2_queue.qsize()).zfill(4)
            else:
                q2_name, q2_size = "", ""

            self.queue_status_table.add_row(q1_name, q1_size, q2_name, q2_size)

    def add_job_task(self, task_info: TaskInfo) -> int:
        """Add a job task to the progress tracker"""
        argc = len(task_info.args) + len(task_info.kwargs)
        argv = ""

        if task_info.args:
            argv = f"{', '.join(map(str, task_info.args))} "

        if task_info.kwargs:
            kwargs_str = " ".join(f"{k}={v}" for k, v in task_info.kwargs.items())
            argv += kwargs_str

        task_description = f"[cyan]{task_info.function.__name__}"

        return self.job_progress.add_task(
            task_description,
            total=100,
            parameters=argv,
            return_value="Pending...",
            start=False
        )

    def mark_task_completed(self, task_id: int, result: str = "Completed"):
        """Mark a task as completed using the managed progress functionality"""
        if hasattr(self.job_progress, 'mark_completed'):
            # Use the custom ManagedProgress method
            self.job_progress.mark_completed(task_id, result)
        else:
            # Fallback to manual management
            if task_id not in self.completed_tasks:
                self.completed_tasks.append(task_id)

            self.job_progress.update(task_id, advance=100, return_value=result)
            self.job_progress.stop_task(task_id)

            if len(self.completed_tasks) > self.max_visible_completed:
                tasks_to_remove = self.completed_tasks[:-self.max_visible_completed]
                for old_task_id in tasks_to_remove:
                    self._hide_completed_task(old_task_id)
                self.completed_tasks = self.completed_tasks[-self.max_visible_completed:]

    def _hide_completed_task(self, task_id: int):
        """Hide a completed task by updating its description (fallback method)"""
        try:
            # Update the task to show it's been archived
            self.job_progress.update(
                task_id,
                description="[dim]Archived",
                return_value="[dim]Task completed and archived"
            )
            # Reset the task to 0 to effectively hide it
            self.job_progress.reset(task_id, total=0)
        except Exception:
            # If all else fails, just ignore the error
            pass

    def get_active_tasks_count(self) -> int:
        """Get count of currently active (non-completed) tasks"""
        total_tasks = len(self.job_progress.tasks) if self.job_progress else 0
        return total_tasks - len(self.completed_tasks)

    def set_max_visible_completed(self, max_count: int):
        """Set the maximum number of completed tasks to keep visible"""
        self.max_visible_completed = max_count
        if hasattr(self.job_progress, 'max_completed_visible'):
            self.job_progress.max_completed_visible = max_count

    def get_completed_tasks_count(self) -> int:
        """Get the number of completed tasks"""
        if hasattr(self.job_progress, 'completed_tasks'):
            return len(self.job_progress.completed_tasks)
        return len(self.completed_tasks)

    def cleanup_all_completed_tasks(self, stats_result: dict = None):
        """Remove all completed tasks from display"""
        if hasattr(self.job_progress, 'cleanup_hidden_tasks'):
            self.job_progress.cleanup_hidden_tasks()
        else:
            for task_id in self.completed_tasks.copy():
                try:
                    self.job_progress.remove_task(task_id)
                except Exception:
                    self._hide_completed_task(task_id)
            self.completed_tasks.clear()
        if stats_result:
            # """Update processing statistics"""
            # records_processed = stats_result['record_count']
            # self.processing_stats.total_records_processed += records_processed

            current_time = time.time()
            elapsed_time = current_time - self.processing_stats.start_time
            time_since_last_update = current_time - self.processing_stats.last_update_time

            # Calculate TPS metrics
            current_tps = stats_result['record_count'] / time_since_last_update if time_since_last_update > 0 else 0
            self.processing_stats.processing_rates.append(current_tps)

            # Keep only last 10 TPS values
            if len(self.processing_stats.processing_rates) > 10:
                self.processing_stats.processing_rates.pop(0)

            avg_tps = sum(self.processing_stats.processing_rates) / len(self.processing_stats.processing_rates)
            completion_pct = (self.processing_stats.total_records_processed /
                              max(stats_result['total'], self.processing_stats.total_records_processed) * 100) if \
            stats_result['total'] > 0 else 0

            # Update stats table
            self.stats_table.columns[1]._cells[0] = f"{self.processing_stats.total_records_processed:,}"
            self.stats_table.columns[1]._cells[1] = f"{elapsed_time:.1f}"
            self.stats_table.columns[1]._cells[2] = f"{current_tps:.1f}"
            self.stats_table.columns[1]._cells[3] = f"{avg_tps:.1f}"
            self.stats_table.columns[1]._cells[4] = f"{completion_pct:.1f}"

    def update_stats(self, stats_result: dict):
        """Update processing statistics"""
        records_processed = stats_result['record_count']
        self.processing_stats.total_records_processed += records_processed

        current_time = time.time()
        elapsed_time = current_time - self.processing_stats.start_time
        time_since_last_update = current_time - self.processing_stats.last_update_time

        # Calculate TPS metrics
        current_tps = records_processed / time_since_last_update if time_since_last_update > 0 else 0
        self.processing_stats.processing_rates.append(current_tps)

        # Keep only last 10 TPS values
        if len(self.processing_stats.processing_rates) > 10:
            self.processing_stats.processing_rates.pop(0)

        avg_tps = sum(self.processing_stats.processing_rates) / len(self.processing_stats.processing_rates)
        completion_pct = (self.processing_stats.total_records_processed /
                          max(stats_result['total'], self.processing_stats.total_records_processed) * 100) if \
        stats_result['total'] > 0 else 0

        # Update stats table
        self.stats_table.columns[1]._cells[0] = f"{self.processing_stats.total_records_processed:,}"
        self.stats_table.columns[1]._cells[1] = f"{elapsed_time:.1f}"
        self.stats_table.columns[1]._cells[2] = f"{current_tps:.1f}"
        self.stats_table.columns[1]._cells[3] = f"{avg_tps:.1f}"
        self.stats_table.columns[1]._cells[4] = f"{completion_pct:.1f}"

        self.processing_stats.last_update_time = current_time

        # Update progress bar
        self.stats_progress.update(self.stats_task_id, advance=records_processed)
        self.stats_progress.update(self.stats_task_id,
                                   total=max(stats_result['total'], self.processing_stats.total_records_processed))


class TaskExecutor:
    """Handles task execution with monitoring"""

    def __init__(self, q_container, my_logger, progress_manager: ProgressManager):
        self.q_container = q_container
        self.my_logger = my_logger
        self.progress_manager = progress_manager
        self.background_tasks = set()

    async def monitor_stats(self, schema: str):
        """Monitor stats queue with improved error handling"""
        asyncio.current_task().set_name(f"stats_monitor_{schema}")
        none_count = 0
        self.q_container.config.override({"schema_name": schema})

        try:
            while True:
                stats_result = {}
                try:
                    stats_result = await priority_queue_manager.get_priority_message(
                        self.q_container.queue_selector()["queue_stats"]
                    )

                    if stats_result.get('isLast', True):
                        none_count += 1

                    if none_count >= stats_result.get('producer_count', 10):
                        self.my_logger.info(f"Stats monitoring completed. Final count: {none_count}")
                        if self.q_container.queue_selector()["queue_stats"].qsize() == 0:
                            break

                    self.progress_manager.update_stats(stats_result)

                except Exception as e:
                    self.my_logger.error(f"Error in stats monitoring: {e}")

                finally:
                    self.q_container.queue_selector()["queue_stats"].task_done()
                    self.progress_manager.cleanup_all_completed_tasks(stats_result)

        except Exception as e:
            self.my_logger.error(f"Stats monitoring failed: {e}")

    async def update_queue_counts(self, schema: str):
        """Update queue status table periodically"""
        asyncio.current_task().set_name(f"queue_monitor_{schema}")
        self.q_container.config.override({"schema_name": schema})
        queue_items = list(self.q_container.queue_selector().items())

        try:
            while not shutdown_event.is_set():
                for i in range(0, len(queue_items), 2):
                    q1_name, q1_queue = queue_items[i]
                    q1_size = str(f"{q1_queue.qsize():04d}")

                    if i + 1 < len(queue_items):
                        q2_name, q2_queue = queue_items[i + 1]
                        q2_size = str(f"{q2_queue.qsize():04d}")
                    else:
                        q2_name, q2_size = "", ""

                    # Update table cells
                    row_index = i // 2
                    if row_index < len(self.progress_manager.queue_status_table.columns[0]._cells):
                        self.progress_manager.queue_status_table.columns[0]._cells[row_index] = q1_name
                        self.progress_manager.queue_status_table.columns[1]._cells[row_index] = q1_size
                        self.progress_manager.queue_status_table.columns[2]._cells[row_index] = q2_name
                        self.progress_manager.queue_status_table.columns[3]._cells[row_index] = q2_size

                await  asyncio.sleep(1)
                # Use cancellable sleep for proper cancellation handling
                # from dags.data_pipeline.utils.async_utils import cancellable_sleep
                # await cancellable_sleep(1, shutdown_event)
                # try:
                #     await asyncio.wait_for(shutdown_event.wait(), timeout=1.0)
                # except asyncio.TimeoutError:
                #     pass
                # except asyncio.CancelledError:
                #     self.my_logger.info("Queue monitoring cancelled")
                #     raise

        except asyncio.CancelledError:
            self.my_logger.info("Queue monitoring cancelled")
        except Exception as err:
            self.my_logger.error(f"Queue monitoring error: {err}")
            raise

    async def execute_task(self, task_info: TaskInfo, task_id: int = None) -> Any:
        """Execute a single task with progress tracking"""
        asyncio.current_task().set_name(f"execute_task_{task_info.function.__name__}")
        try:
            self.my_logger.debug(f"Processing {task_info.function.__name__}")

            if task_id is not None:
                self.progress_manager.job_progress.start_task(task_id)

            result = None

            # Special handling for process_jira_issues
            if task_info.function.__name__ == "process_jira_issues":
                schema = task_info.args[0] if task_info.args else ""

                # Start background monitoring tasks
                queue_task = asyncio.create_task(
                    self.update_queue_counts(schema),
                    name=f"queue_monitor_{schema}"
                )
                stats_task = asyncio.create_task(
                    self.monitor_stats(schema),
                    name=f"monitor_stats_{schema}"
                )

                self.background_tasks.update([queue_task, stats_task])

                try:
                    self.my_logger.info(f"Starting {task_info.function.__name__}")
                    result = await task_info.function(*task_info.args, **task_info.kwargs)
                    self.my_logger.info(f"{task_info.function.__name__} completed")

                finally:
                    # Clean up background tasks
                    for task in [queue_task, stats_task]:
                        if not task.done():
                            task.cancel()
                            try:
                                await task
                            except asyncio.CancelledError:
                                pass

                    self.background_tasks.discard(queue_task)
                    self.background_tasks.discard(stats_task)
            else:
                # Regular task execution
                if asyncio.iscoroutinefunction(task_info.function):
                    result = await task_info.function(*task_info.args, **task_info.kwargs)
                else:
                    result = task_info.function(*task_info.args, **task_info.kwargs)

            if task_id is not None:
                self.progress_manager.job_progress.update(
                    task_id, advance=100, return_value=str(result)
                )
                self.progress_manager.job_progress.stop_task(task_id)

            self.my_logger.debug(f"Completed {task_info.function.__name__}")
            return result

        except Exception as e:
            self.my_logger.error(f"Error executing {task_info.function.__name__}: {e}")
            if task_id is not None:
                self.progress_manager.job_progress.update(
                    task_id, advance=100, return_value=f"Error: {str(e)[:50]}"
                )
            raise

    async def cleanup(self):
        """Clean up background tasks"""
        asyncio.current_task().set_name("TaskExecutor.cleanup")
        for task in list(self.background_tasks):
            if not task.done():
                task.cancel()
                try:
                    # await task
                    await asyncio.wait_for(task, timeout=2.0)
                except asyncio.TimeoutError:
                    print(f"⚠️ Timeout waiting for {task.get_name()} to cancel.")
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    print(f"❌ Error while cancelling task {task.get_name()}: {e}")
        self.background_tasks.clear()


def get_task_state(task):
    """Get detailed task state information"""
    if hasattr(task, '_state'):
        # Task states: PENDING, RUNNING, DONE, CANCELLED
        return task._state
    elif task.done():
        if task.cancelled():
            return 'CANCELLED'
        elif task.exception():
            return 'EXCEPTION'
        else:
            return 'DONE'
    else:
        return 'PENDING'


def detect_await_state(task):
    """Detect exactly what a task is awaiting with enhanced granular details"""
    try:
        # Check if task has a waiter (what it's waiting for)
        if hasattr(task, '_fut_waiter') and task._fut_waiter:
            waiter = task._fut_waiter
            return f"Waiting for: {type(waiter).__name__}"

        # Get detailed await location and function name
        await_location, await_target = _get_await_location_details(task)
        if await_location != "unknown":
            return f"{await_location} -> {await_target}"

        # Check task state
        if hasattr(task, '_state'):
            if task._state == 'PENDING':
                # Task is in await, inspect stack
                stack = task.get_stack()
                if stack:
                    # Find the await line with more details
                    for frame in stack:
                        filename = frame.f_code.co_filename
                        lineno = frame.f_lineno
                        func_name = frame.f_code.co_name

                        # Skip asyncio internals for cleaner output
                        if 'asyncio' not in filename and 'await' in func_name:
                            return f"{filename}:{lineno} in {func_name}"

        return ""
    except Exception as e:
        return f"Error detecting await state: {e}"


def _get_await_location_details(task):
    """Get detailed await location and target from task stack"""
    try:
        stack = task.get_stack()
        if not stack:
            return "no_stack", "no_target"

        await_location = "unknown"
        await_target = "unknown"

        for frame in stack:
            filename = frame.f_code.co_filename
            lineno = frame.f_lineno
            func_name = frame.f_code.co_name

            # Skip asyncio internals for cleaner output
            if 'asyncio' not in filename:
                location = f"{filename.split('/')[-1]}:{lineno} in {func_name}"

                # Try to get the actual line of code being awaited
                try:
                    import linecache
                    line = linecache.getline(filename, lineno).strip()
                    if 'await' in line:
                        await_location = location
                        # Extract what's being awaited
                        await_parts = line.split('await')
                        if len(await_parts) > 1:
                            await_target = await_parts[1].strip().split('(')[0]
                        break
                except Exception:
                    pass

        return await_location, await_target

    except Exception as e:
        return f"error: {e}", "error"


# ============================================================================
# TASK LIFECYCLE COORDINATOR FOR GRACEFUL SHUTDOWN
# ============================================================================

@dataclass
class TaskInfo:
    """Information about a task to be executed."""
    function: Callable
    args: tuple = ()
    kwargs: dict = field(default_factory=dict)





# Enhanced monitor with multiple monitoring views and database monitoring
@inject
async def monitor(
        task_limit_sec, exclude_names=(),
        monitor_panel=None,
        system_metrics_collector: SystemMetricsCollector = None,
        async_process_tracker: AsyncProcessTracker = None,
        task_coordinator: Optional[TaskLifecycleCoordinator] = None,
        app_container: DynamicContainer = Provide[ApplicationContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    """Enhanced monitor with granular task monitoring and database monitoring"""
    asyncio.current_task().set_name("monitor")
    task_dict = dict()
    task_states = dict()  # Track task state changes
    task_await_details = dict()  # Track await details
    view_rotation_index = 0
    view_change_interval = 2  # Change view every 2 seconds for higher frequency
    last_view_change = time.time()

    # View frequency weights: async processes get more time, add database view
    view_weights = [1, 1, 4, 2]  # [long_running, system, async_processes, database]
    current_weight_cycle = 0

    # Initialize collectors if not provided
    if system_metrics_collector is None:
        system_metrics_collector = SystemMetricsCollector()
    if async_process_tracker is None:
        async_process_tracker = AsyncProcessTracker()

    # Database monitoring setup
    db_metrics = {}
    last_db_check = 0

    def create_long_running_tasks_table():
        """Create enhanced table for long-running tasks with granular details"""
        monitor_tb = Table(
            title="Long-Running Tasks (Granular Monitoring)",
            show_header=True,
            header_style="bold magenta"
        )
        monitor_tb.add_column("Task Name", style="cyan", width=25)
        monitor_tb.add_column("Duration (s)", style="magenta", width=12)
        monitor_tb.add_column("State", style="green", width=12)
        monitor_tb.add_column("Await Location", style="yellow", width=30)
        monitor_tb.add_column("Function", style="blue", width=20)
        return monitor_tb

    def create_system_metrics_table(metrics: SystemMetrics):
        """Create table for system metrics"""
        system_tb = Table(
            title="System Parameters",
            show_header=True,
            header_style="bold green"
        )
        system_tb.add_column("Metric", style="cyan")
        system_tb.add_column("Value", style="green")

        memory_gb = metrics.memory_usage_mb / 1024
        system_tb.add_row("Memory Usage", f"{memory_gb:.1f} GB ({metrics.memory_percent:.1f}%)")
        system_tb.add_row("CPU Usage", f"{metrics.cpu_percent:.1f}%")
        system_tb.add_row("Asyncio Tasks", str(metrics.asyncio_tasks_count))
        system_tb.add_row("HTTP Requests", str(metrics.http_requests_count))
        system_tb.add_row("TCP Connections", str(metrics.tcp_connections_count))
        system_tb.add_row("Retry Count", str(metrics.retry_count))
        system_tb.add_row("Failure Count", str(metrics.failure_count))

        return system_tb

    def create_async_processes_table(processes: List[AsyncProcessInfo]):
        """Create enhanced table for async processes with producer details"""
        process_tb = Table(
            title="Async Processes (Producer/Consumer Details)",
            show_header=True,
            header_style="bold yellow"
        )
        process_tb.add_column("Name", style="cyan", width=30)
        process_tb.add_column("Type", style="blue", width=12)
        process_tb.add_column("Status", style="green", width=12)
        process_tb.add_column("Records", style="magenta", width=10)
        process_tb.add_column("Runtime (s)", style="yellow", width=12)
        process_tb.add_column("Details", style="white", width=25)

        current_time = time.time()
        for process in processes:
            # Calculate runtime only if process has started
            if process.status == "not_running":
                runtime_str = "N/A"
            else:
                runtime = current_time - process.start_time
                runtime_str = f"{runtime:.1f}"
            
            # Set status color based on status
            if process.status == "running":
                status_style = "green"
            elif process.status == "failed":
                status_style = "red"
            elif process.status == "not_running":
                status_style = "dim white"
            elif process.status == "completed":
                status_style = "blue"
            else:
                status_style = "yellow"
            
            # Enhanced details for producer/consumer identification
            details = ""
            if "producer" in process.name.lower():
                details = f"P-{process.schema}"
            elif "consumer" in process.name.lower():
                details = f"C-{process.schema}"
            elif process.schema:
                details = process.schema

            process_tb.add_row(
                process.name[:20] + "..." if len(process.name) > 20 else process.name,
                process.process_type,
                f"[{status_style}]{process.status}[/{status_style}]",
                str(process.records_processed) if process.records_processed > 0 else "N/A",
                runtime_str,
                details[:20] + "..." if len(details) > 20 else details
            )

        return process_tb

    def create_database_monitoring_table(db_metrics: dict):
        """Create table for database monitoring"""
        db_tb = Table(
            title="Database Monitoring (High Frequency)",
            show_header=True,
            header_style="bold blue"
        )
        db_tb.add_column("Metric", style="cyan", width=20)
        db_tb.add_column("Value", style="blue", width=15)
        db_tb.add_column("Status", style="green", width=15)

        if db_metrics:
            db_tb.add_row("Active Connections", str(db_metrics.get('active_connections', 'N/A')), "Normal")
            db_tb.add_row("Lock Count", str(db_metrics.get('total_locks', 'N/A')), "Normal")
            db_tb.add_row("Database Size", str(db_metrics.get('database_size', 'N/A')), "Normal")
            db_tb.add_row("Deadlocks", str(db_metrics.get('deadlocks', 'N/A')), "Normal")
            db_tb.add_row("Last Check", str(db_metrics.get('last_check', 'N/A')), "Normal")
        else:
            db_tb.add_row("Status", "No Data", "Warning")

        return db_tb

    async def collect_database_metrics():
        """Collect database metrics for monitoring"""
        try:
            async with app_container.database_rw().update_schema('public').async_session() as session:
                # Connection count
                conn_query = text("""
                    SELECT count(*) as active_connections
                    FROM pg_stat_activity
                    WHERE state = 'active'
                """)

                # Lock information
                lock_query = text("""
                    SELECT count(*) as total_locks
                    FROM pg_locks
                """)

                # Database size
                size_query = text("""
                    SELECT pg_size_pretty(pg_database_size(current_database())) as db_size
                """)

                conn_result = await session.execute(conn_query)
                lock_result = await session.execute(lock_query)
                size_result = await session.execute(size_query)

                return {
                    'active_connections': conn_result.scalar(),
                    'total_locks': lock_result.scalar(),
                    'database_size': size_result.scalar(),
                    'deadlocks': 0,  # Placeholder
                    'last_check': time.strftime('%H:%M:%S')
                }
        except Exception as e:
            my_logger.error(f"Error collecting database metrics: {e}")
            return {}

    while not shutdown_event.is_set():
        # Check for shutdown request from task coordinator
        if task_coordinator and task_coordinator.is_shutdown_requested():
            my_logger.info("Monitor task stopping due to task coordinator shutdown request")
            break
        # Legacy shutdown check
        # global shutdown_requested
        # if shutdown_requested:
        #     my_logger.info("Monitor task stopping due to shutdown request")
        #     break
            
        current_time = time.time()

        # Determine which view to show based on weighted rotation
        if current_time - last_view_change >= view_change_interval:
            current_weight_cycle += 1

            # Determine view based on weights
            if current_weight_cycle <= view_weights[0]:
                view_rotation_index = 0  # Long-running tasks
            elif current_weight_cycle <= view_weights[0] + view_weights[1]:
                view_rotation_index = 1  # System metrics
            else:
                view_rotation_index = 2  # Async processes

            # Reset cycle when all weights are exhausted
            if current_weight_cycle >= sum(view_weights):
                current_weight_cycle = 0

            last_view_change = current_time

        try:
            if view_rotation_index == 0:
                # Enhanced long-running tasks view with granular monitoring
                monitor_table = create_long_running_tasks_table()

                # Get all tasks using asyncio.all_tasks() for real-time data
                tasks = [t for t in asyncio.all_tasks() if t.get_name() not in exclude_names]

                for task in tasks:
                    name = task.get_name()
                    current_state = get_task_state(task)

                    # Track task state changes
                    if name not in task_states:
                        task_states[name] = current_state
                        task_dict[name] = time.monotonic()
                        continue

                    # Check for state changes
                    if task_states[name] != current_state:
                        my_logger.debug(f"Task {name} state changed: {task_states[name]} -> {current_state}")
                        task_states[name] = current_state

                    duration = time.monotonic() - task_dict[name]
                    if duration > task_limit_sec:
                        # Get enhanced await details
                        await_location, await_target = _get_await_location_details(task)

                        # Store await details for tracking
                        task_await_details[name] = {
                            'location': await_location,
                            'target': await_target,
                            'state': current_state
                        }

                        monitor_table.add_row(
                            name[:25] + "..." if len(name) > 25 else name,
                            f"{duration:.3f}",
                            current_state,
                            await_location[:30] + "..." if len(await_location) > 30 else await_location,
                            await_target[:20] + "..." if len(await_target) > 20 else await_target
                        )

                if monitor_panel:
                    monitor_panel.update(Panel(monitor_table, title="Granular Task Monitor", border_style="red"))

            elif view_rotation_index == 1:
                # System metrics view
                metrics = system_metrics_collector.collect_metrics()
                system_table = create_system_metrics_table(metrics)

                if monitor_panel:
                    monitor_panel.update(Panel(system_table, title="System Monitor", border_style="green"))

            elif view_rotation_index == 2:
                # Enhanced async processes view with producer/consumer details
                async_process_tracker.sync_with_asyncio_tasks()  # Sync with real-time tasks
                rotated_processes = async_process_tracker.get_rotated_processes(max_display=8)
                process_table = create_async_processes_table(rotated_processes)

                if monitor_panel:
                    monitor_panel.update(Panel(process_table, title="Producer/Consumer Monitor", border_style="yellow"))

            else:
                # Database monitoring view (high frequency)
                current_time = time.time()
                if current_time - last_db_check > 0.5:  # Check every 0.5 seconds
                    db_metrics = await collect_database_metrics()
                    last_db_check = current_time

                db_table = create_database_monitoring_table(db_metrics)

                if monitor_panel:
                    monitor_panel.update(Panel(db_table, title="Database Monitor", border_style="blue"))

        except Exception as e:
            my_logger.error(f"Error in monitor: {e}")
            # Fallback to simple display
            if monitor_panel:
                monitor_panel.update(Panel(f"Monitor error: {e}", title="Monitor Error", border_style="red"))

        # Use cancellable sleep for proper cancellation handling
        # from dags.data_pipeline.utils.async_utils import cancellable_sleep
        # await cancellable_sleep(1, shutdown_event)
        try:
            await asyncio.wait_for(shutdown_event.wait(), timeout=1.0)
        except asyncio.TimeoutError:
            pass
        except asyncio.CancelledError:
            my_logger.info("Monitor task cancelled")
            raise



@inject
async def process_task(
        display_rich: bool = True,
        projects: tuple = (),
        # db_rw=Provide[DatabaseSessionManagerContainer.database_rw_enhanced],
        # db_ro=Provide[DatabaseSessionManagerContainer.database_ro_enhanced],
        app_container: DynamicContainer = Provide[ApplicationContainer],
        q_container: DynamicContainer = Provide[QueueContainer],
        my_logger: Logger = Provide[LoggerContainer.logger]
):
    """Refactored process_task with TaskLifecycleCoordinator for graceful shutdown"""

    # Set up current task name
    asyncio.current_task().set_name("process_task")

    # Initialize task lifecycle coordinator
    task_coordinator = TaskLifecycleCoordinator(my_logger)

    # Get project list
    # schemas = q_container.schemas()
    project_list = projects

    # Define tasks using TaskInfo dataclass
    task_definitions = [
        # TaskInfo(create_db_extension, [], {}),
        # *[TaskInfo(create_schema_tables_ddl, [project, db_rw, db_ro, my_logger], {}) for project in project_list],
        # TaskInfo(get_deleted_worklog, [], {}),
        # TaskInfo(get_fields, [], {}),
        # TaskInfo(get_jira_users, [], {}),
        # TaskInfo(get_all_jira_boards, [], {}),
        # *[TaskInfo(process_jira_versions, [project], {}) for project in project_list],
        # *[TaskInfo(get_sprint_details, [project], {}) for project in project_list],
        *[TaskInfo(process_jira_issues, [project, 'recon', True], {}) for project in project_list],
        # *[TaskInfo(upsert_issue_classification, [project], {}) for project in project_list],
        # *[TaskInfo(delete_worklog, [project], {}) for project in project_list],
        # TaskInfo(create_refresh_mv, [project_list], {})
    ]

    # Initialize managers
    display_manager = RichDisplayManager()
    progress_manager = ProgressManager()
    # Configure to show only 2 completed tasks instead of 4
    progress_manager.set_max_visible_completed(2)

    task_executor = TaskExecutor(q_container, my_logger, progress_manager)

    # Check how many tasks are completed
    completed_count = progress_manager.get_completed_tasks_count()
    my_logger.info(f"Completed tasks count: {completed_count}")

    try:
        if display_rich:
            # Initialize all UI components
            progress_manager.initialize_progress_bars(len(task_definitions))
            progress_manager.initialize_stats_table()
            if project_list:
                progress_manager.initialize_queue_table(project_list[0], q_container)

            # Create layout
            layout = display_manager.create_layout()

            # Add job tasks to progress tracker
            job_tasks = []
            for task_info in task_definitions:
                task_id = progress_manager.add_job_task(task_info)
                job_tasks.append(task_id)

            # Set up layout panels
            layout["overall"].update(Panel(
                Align.center(progress_manager.overall_progress, vertical="middle"),
                title="Overall Progress", border_style="green"
            ))

            if progress_manager.queue_status_table:
                layout["queues"].update(Panel(
                    progress_manager.queue_status_table,
                    title="Queue Status", border_style="cyan"
                ))

            layout["stats_progress"].update(Panel(
                Align.center(progress_manager.stats_progress, vertical="middle"),
                title="Stats Progress", border_style="yellow"
            ))


            layout["stats_table"].update(Panel(
                progress_manager.stats_table,
                title="Processing Statistics", border_style="magenta"
            ))

            layout["tasks"].update(Panel(
                Align.center(progress_manager.job_progress, vertical="middle"),
                title="[b]Task Progress", border_style="red"
            ))

            layout["monitor"].update(Panel(
                "No tasks exceeding the limit.",
                title="Task Monitor", border_style="red"
            ))

            # Start monitor task with enhanced monitoring
            monitor_task = asyncio.create_task(
                monitor(
                    task_limit_sec=10,
                    exclude_names=("monitor",),
                    monitor_panel=layout["monitor"],
                    system_metrics_collector=progress_manager.system_metrics_collector,
                    async_process_tracker=progress_manager.async_process_tracker,
                    task_coordinator=task_coordinator  # Pass coordinator to monitor
                ),
                name="monitor"
            )

            app_container.schema.override('public')

            # Use enhanced session manager with connection recovery
            enhanced_session_manager = app_container.database_rw_enhanced().update_schema('public')
            async_session_factory = enhanced_session_manager.async_session

            async with enhanced_session_manager.async_session() as db_session:
                # db_monitor = DatabaseTaskMonitor(db_session)
                # db_monitor_task = asyncio.create_task(
                #     db_monitor.start_monitoring(interval=0.1),
                #     name="db_monitor"
                # )

                # Register monitors with coordinator
                await task_coordinator.register_monitor(monitor_task, "monitor")
                # await task_coordinator.register_monitor(db_monitor_task, "db_monitor")

                # Execute tasks with Rich UI and coordinator
                async with display_manager.managed_live(layout):
                    await _execute_all_tasks_with_coordinator(
                        task_definitions, job_tasks, task_executor,
                        progress_manager, task_coordinator, my_logger
                    )

                # Wait for all tasks to complete and monitors to shutdown gracefully
                await task_coordinator.wait_for_all_tasks_completion(timeout=30.0)

        else:
            # Execute tasks without Rich UI
            await _execute_all_tasks(
                task_definitions, [], task_executor,
                progress_manager, my_logger
            )

    finally:
        # Ensure cleanup
        await task_executor.cleanup()
        my_logger.info("Process task completed")


async def _execute_all_tasks(
        task_definitions: List[TaskInfo],
        job_tasks: List[int],
        task_executor: TaskExecutor,
        progress_manager: ProgressManager,
        my_logger
):
    """Execute all tasks with progress tracking"""
    # global shutdown_requested
    
    for i, task_info in enumerate(task_definitions):
        # Check if shutdown was requested
        # if shutdown_requested:
        #     my_logger.info(f"Shutdown requested. Stopping task execution at task {i + 1}/{len(task_definitions)}")
        #     break
            
        try:
            task_id = job_tasks[i] if i < len(job_tasks) else None
            result = await task_executor.execute_task(task_info, task_id)

            if progress_manager.overall_progress:
                progress_manager.overall_progress.update(
                    progress_manager.overall_task_id, completed=i + 1
                )

            # Clean up completed tasks every 3 completed tasks to keep display manageable
            if (i + 1) % 3 == 0:
                progress_manager.cleanup_all_completed_tasks()
                my_logger.debug(f"Cleaned up completed tasks at iteration {i + 1}")

        except Exception as e:
            my_logger.error(f"Task execution failed: {e}")
            # Continue with next task instead of failing completely
            continue
        finally:
            # Final cleanup at the end
            progress_manager.cleanup_all_completed_tasks()
            my_logger.info("Final cleanup of all completed tasks")


async def _execute_all_tasks_with_coordinator(
        task_definitions: List[TaskInfo],
        job_tasks: List[int],
        task_executor: TaskExecutor,
        progress_manager: ProgressManager,
        task_coordinator: TaskLifecycleCoordinator,
        my_logger
):
    """Execute all tasks with TaskLifecycleCoordinator for graceful shutdown"""

    for i, task_info in enumerate(task_definitions):
        # Check if shutdown was requested
        if task_coordinator.is_shutdown_requested():
            my_logger.info(f"Shutdown requested. Stopping task execution at task {i + 1}/{len(task_definitions)}")
            break

        try:
            # Register task with coordinator
            task_name = task_info.function.__name__
            coordinator_task_id = await task_coordinator.register_task(task_name)

            # Execute task
            task_id = job_tasks[i] if i < len(job_tasks) else None
            result = await task_executor.execute_task(task_info, task_id)

            # Mark task as completed in coordinator
            await task_coordinator.mark_task_completed(coordinator_task_id, result)

            if progress_manager.overall_progress:
                progress_manager.overall_progress.update(
                    progress_manager.overall_task_id, completed=i + 1
                )

            # Clean up completed tasks every 3 completed tasks to keep display manageable
            if (i + 1) % 3 == 0:
                progress_manager.cleanup_all_completed_tasks()
                my_logger.debug(f"Cleaned up completed tasks at iteration {i + 1}")

        except Exception as e:
            my_logger.error(f"Error executing task {task_info.function.__name__}: {e}")

            # Mark task as failed in coordinator
            if 'coordinator_task_id' in locals():
                await task_coordinator.mark_task_failed(coordinator_task_id, e)

            # Continue with next task instead of breaking
            continue
        finally:
            # Final cleanup at the end
            progress_manager.cleanup_all_completed_tasks()

    my_logger.info(f"Task execution completed. Active: {task_coordinator.get_active_task_count()}, "
                  f"Completed: {task_coordinator.get_completed_task_count()}")


@inject
async def test_function(
        my_logger: Logger = Provide[LoggerContainer.logger],
        db_rw=Provide[DatabaseSessionManagerContainer.database_rw],
        app_container=Provide[ApplicationContainer],
        kp: PyKeePass = Provide[KeePassContainer.keepass_manager],
        extractor=Provide[IssueFieldsContainer.field_name_extractor],
        q_container: DynamicContainer = Provide[QueueContainer],
):
    # schemas = await app_container.database_rw().get_schemas_async()
    # print("Schemas:", schemas)
    #
    print(f"kp type = {type(kp)}")

    schemas = queue_container.schemas()
    print("Schemas:", schemas)
    q_container.config.override({"schema_name": "plat"})
    rprint(type(q_container.queue_selector()["queue_issues"]))

    x = app_container.database_rw().get_schemas()
    if 'information_schema' in x:
        x.remove('information_schema')

    rprint(f"schema list: {x}")
    rprint(f"schema list: {await app_container.database_rw().get_schemas_async()}")
    rprint(type(kp))
    rprint(app_container.config()["Database"])

    with app_container.database_rw().session() as pg_session:
        rprint(f"{dir(pg_session.bind.engine)}")
        rprint(f"Execution options = {pg_session.bind.engine.get_execution_options()}")
        rprint(f"Execution options = {pg_session.bind.engine.execution_options}")
        with pg_session.begin():
            stmt = select(func.count()).select_from(Issue)
            out = pg_session.execute(stmt).all()
            rprint(out)

    with db_rw.session() as pg_session:
        with pg_session.begin():
            rprint(pg_session.bind.url)
            pool = pg_session.bind.engine.pool

            idle_connections = pool._pool.qsize()
            # total_connections = len(pool._holders)
            # in_use = total_connections - idle_connections
            # Checking pool size
            rprint(f"Pool size: {pool.size()}")

            # Checking number of idle connections
            rprint(f"Idle connections: {pool.checkedin()}")

            # Checking the number of overflow connections
            rprint(f"Overflow connections: {pool.overflow()}")
            # Total: {total_connections}, In - use: {in_use}
            my_logger.info(
                f"Pool stats - , Idle: {idle_connections}"
            )




    # End of function


def cleanup_role(pg_session, role_name):
    try:
        # Revoke table privileges
        rprint(f"role name = {role_name}")
        revoke_tables = """
        DO $$
        DECLARE
            obj RECORD;
        BEGIN
            FOR obj IN
                SELECT table_schema, table_name
                FROM information_schema.role_table_grants
                WHERE grantee = :role_name
            LOOP
                EXECUTE format(
                'REVOKE ALL PRIVILEGES ON TABLE %I.%I FROM %I', obj.table_schema, obj.table_name, :role_name
                );
            END LOOP;
        END $$;
        """
        pg_session.execute(text(revoke_tables), {"role_name": role_name})

        # Revoke sequence privileges
        revoke_sequences = """
        DO $$
        DECLARE
            obj RECORD;
        BEGIN
            FOR obj IN
                SELECT schemaname AS sequence_schema, sequencename AS sequence_name
                FROM pg_sequences
                WHERE schemaname NOT IN ('pg_catalog', 'information_schema')
            LOOP
                EXECUTE format(
                'REVOKE ALL PRIVILEGES ON SEQUENCE %I.%I FROM %I', obj.sequence_schema, obj.sequence_name, :role_name
                );
            END LOOP;
        END $$;
        """
        pg_session.execute(text(revoke_sequences), {"role_name": role_name})

        # Revoke permission for materialized views
        revoke_mv_privileges = f"""
        DO $$ 
        DECLARE 
            r RECORD;
        BEGIN 
            FOR r IN (SELECT schemaname, matviewname FROM pg_catalog.pg_matviews) 
            LOOP 
                EXECUTE format('REVOKE ALL PRIVILEGES ON %I.%I FROM %I', r.schemaname, r.matviewname, :role_name);
            END LOOP; 
        END $$;
        """
        pg_session.execute(text(revoke_mv_privileges), {"role_name": role_name})

        # Revoke schema-level privileges
        revoke_privileges = f"""
            REVOKE ALL PRIVILEGES ON SCHEMA public FROM {role_name}
        """
        pg_session.execute(text(revoke_privileges))

        # Drop the role
        pg_session.execute(text(f"DROP ROLE IF EXISTS {role_name}"))
        pg_session.commit()
        print(f"Role {role_name} cleaned up and dropped successfully.")

    except Exception as e:
        print(f"Error cleaning up role {role_name}: {e}")
        pg_session.rollback()


@inject
def cleanup_entries(
        schema_name: str,
        kp: PyKeePass = Provide[KeePassContainer.keepass_manager],
        app_container: DynamicContainer = Provide[ApplicationContainer]

):
    password = Prompt.ask("Enter your password", password=True)

    if verify_password(
            password,
            "$argon2id$v=19$m=65536,t=3,p=4$NCYkBOAcI6S01npPSan1fg$dFRfGkx5J9HbzljOzijM53ywRfqQb7MA+leBXXnH8YM"
    ):
        proceed = Confirm.ask(f"Do you want to clean-up {schema_name} entries")
        if proceed:
            entry = kp.find_entries(title=f"{schema_name}_rw", first=True)
            if entry:
                kp.delete_entry(entry)
                rprint(f"Deleted {schema_name}_rw")
                kp.save()

            entry = kp.find_entries(title=f"{schema_name}_ro", first=True)
            if entry:
                kp.delete_entry(entry)
                rprint(f"Deleted {schema_name}_ro")
                kp.save()

            try:
                app_container.schema.override('public')
                with app_container.database_rw().update_schema('public').session() as pg_session:
                    # Drop schema
                    pg_session.execute(text(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE"))
                    pg_session.commit()
                    rprint(f"Schema {schema_name} dropped successfully.")

                    # Drop roles
                    cleanup_role(pg_session, f"{schema_name}_rw")
                    rprint(f"Role {schema_name}_rw dropped successfully.")
                    cleanup_role(pg_session, f"{schema_name}_ro")
                    rprint(f"Role {schema_name}_ro dropped successfully.")
                    pg_session.commit()
            except Exception as e:
                handle_exception(e)
        else:
            rprint(f"User opted No")

    else:
        rprint("[red]Authentication Failed[/red]")

    # if kp.find_entries(title=f"{schema_name}_rw", first=True):
    #


@inject
def check_upsert(
        app_container: DynamicContainer = Provide[ApplicationContainer]
):
    now = datetime.now().isoformat()  # Convert to ISO format
    date_literal = literal(now)
    rprint(f"date_literal = {date_literal}")

    # Compile to SQL
    compiled_literal = date_literal.compile(dialect=dialect(), compile_kwargs={"literal_binds": True})
    rprint(compiled_literal)

    data = [
        [136529, "PLAT-35697", "Test", "2022-03-23", "2022-03-21"]
    ]
    df = pd.DataFrame(data, columns=["initiative_id", "initiative_key", "project", "created", "updated"])
    df["created"] = pd.to_datetime(df["created"])
    df["updated"] = pd.to_datetime(df["updated"])

    conflict_condition = and_(
        InitiativeAttribute.updated < insert(InitiativeAttribute).excluded.updated
    )

    with (app_container.database_rw().update_schema('plat').session() as pg_session):
        upsert(
            pg_session, InitiativeAttribute, df, no_update_cols=("release", "feature", "created"),
            # conflict_condition=["updated"],
            conflict_condition=conflict_condition,
        )
        stmt = select(
            InitiativeAttribute.project, InitiativeAttribute.updated
        ).filter(InitiativeAttribute.initiative_id == 136529)
        result = pg_session.execute(stmt)
        rprint(result.fetchall())
        pg_session.rollback()

        stmt = insert(InitiativeAttribute).values(df.to_dict(orient="records"))
        stmt = stmt.on_conflict_do_update(
            index_elements=["initiative_id"],
            where=conflict_condition,
            set_={
                "initiative_key": stmt.excluded.initiative_key,
                "project": stmt.excluded.project,
                "updated": stmt.excluded.updated,
            },
        )
        pg_session.execute(stmt)
        rprint(f"compiled query")
        rprint(compile_query(stmt))

        stmt = select(InitiativeAttribute.project, InitiativeAttribute.updated).filter(
            InitiativeAttribute.initiative_id == 136529)
        result = pg_session.execute(stmt)
        rprint(result.fetchall())
        pg_session.rollback()


@inject
async def split_jql_by_count(
        jql: str,
        max_batch_size: int = 1000,
        num_batches: int = 10,
        jira_entry: EntryDetails = Provide[JiraEntryDetailsContainer.jira_entry_details],
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> tuple[list[str], int]:
    """
        Splits a JQL query into multiple batches based on date ranges to handle large result sets.

        This function will adaptively split the JQL query into smaller batches that each return
        a more manageable number of results. It uses approximate count API calls to distribute
        records evenly across batches and ensures each batch contains records.

        Args:
            jql: The original JQL query string that needs to be split
            max_batch_size: Maximum number of records allowed in a single batch
            num_batches: Target number of batches to split the query into
            jira_entry: JIRA connection details
            my_logger: Logger instance for tracking progress and errors

        Returns:
            A list of JQL strings representing the batched queries, or an empty list if splitting
            is not needed or not possible
        """
    try:
        my_logger.info(f"Starting JQL split operation with target of {num_batches} batches")
        my_logger.debug(f"Original JQL: {jql}")

        # Check approximate count
        timeout = aiohttp.ClientTimeout(total=300)
        connector = aiohttp.TCPConnector(
            limit=50, limit_per_host=25, resolver=aiohttp.AsyncResolver(),
            ssl=ssl.create_default_context()
        )
        async with aiohttp.ClientSession(
            headers=jira_entry.custom_properties, timeout=timeout,
            connector=connector, raise_for_status=True
        ) as http_session:
            response = await fetch_with_retries_post(
                http_session,
                f"{jira_entry.url}/rest/api/3/search/approximate-count",
                json_payload={"jql": jql}
            )

            if not response or not response.get('success'):
                my_logger.error(f"Failed to get approximate count: {response.get('exception') if response else 'No response'}")
                return [], -1

            result = response.get('result', {})
            total_count = result.get('count', 0)
            my_logger.info(f"Approximate count for JQL: {total_count}")

            # If count is less than max_batch_size, return original JQL
            if total_count <= max_batch_size:
                return [jql], total_count

            # Extract date range from JQL if it exists
            # Match both created and updated fields
            date_pattern = r'(created|updated)\s*>\s*[\'"]([^\'"]*)[\'"]\s*(?:and\s*(created|updated)\s*<\s*[\'"]([^\'"]*)[\'"]\s*)?'
            match = re.search(date_pattern, jql)

            if not match:
                my_logger.warning("Could not find date range in JQL, cannot split")
                return [], -1

            field_start, start_date_str, field_end, end_date_str = match.groups()

            # If end date is not specified, use current date
            if not end_date_str:
                end_date = datetime.now()
                field_end = field_start  # Use same field for end date
            else:
                try:
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d %H:%M')
                except ValueError:
                    try:
                        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                    except ValueError:
                        my_logger.error(f"Could not parse end date: {end_date_str}")
                        return [], -1

            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d %H:%M')
            except ValueError:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                except ValueError:
                    my_logger.error(f"Could not parse start date: {start_date_str}")
                    return [], -1

            # Use binary search to find date intervals that more evenly distribute records
            return await adaptive_split_jql(jql, start_date, end_date, field_start, field_end,
                                            num_batches, http_session, jira_entry, my_logger), total_count

    except Exception as e:
        my_logger.error(f"Error splitting JQL: {e}", exc_info=True)
        return [], -1


async def adaptive_split_jql(
        jql: str,
        start_date: datetime,
        end_date: datetime,
        field_start: str,
        field_end: str,
        num_batches: int,
        http_session,
        jira_entry: EntryDetails,
        my_logger: Logger
) -> list[str]:
    """
    Adaptively split JQL by finding date boundaries that better distribute records.

    This function uses a binary search approach to find date boundaries that evenly
    distribute records across batches. It validates each batch to ensure it contains
    records and excludes any empty batches.

    Args:
        jql: Original JQL query string
        start_date: Start date boundary for the query
        end_date: End date boundary for the query
        field_start: Field name for the start date criteria (created/updated)
        field_end: Field name for the end date criteria (created/updated)
        num_batches: Target number of batches to create
        http_session: Active HTTP client session
        jira_entry: JIRA connection details
        my_logger: Logger instance for tracking progress

    Returns:
        List of JQL strings representing valid batches (with records)
    """
    total_seconds = (end_date - start_date).total_seconds()

    # First get total count for the full date range
    full_range_jql = create_date_range_jql(jql, start_date, end_date, field_start, field_end)
    full_count_response = await fetch_with_retries_post(
        http_session,
        f"{jira_entry.url}/rest/api/3/search/approximate-count",
        json_payload={"jql": full_range_jql}
    )

    if not full_count_response or not full_count_response.get('success'):
        my_logger.error(f"Failed to get full range count: {full_count_response}")
        return []

    total_count = full_count_response['result']['count']
    if total_count == 0:
        return []

    # If total count is still manageable, just return original JQL
    if total_count <= num_batches:
        return [full_range_jql]

    # Find split points that distribute records more evenly
    target_per_batch = total_count / num_batches
    my_logger.info(f"Targeting approximately {target_per_batch} records per batch")

    # Find batch boundaries
    batch_boundaries = [start_date]
    current_date = start_date

    for i in range(1, num_batches):
        # Binary search to find a date that gives approximately i*target_per_batch records
        target_records = int(i * target_per_batch)
        low_date = current_date
        high_date = end_date

        # Maximum 10 iterations of binary search to prevent excessive API calls
        mid_date = low_date + (high_date - low_date) / 2
        for _ in range(10):
            mid_date = low_date + (high_date - low_date) / 2

            # Create JQL for records from start_date to mid_date
            test_jql = create_date_range_jql(jql, start_date, mid_date, field_start, field_end)

            # Get count for this date range
            count_response = await fetch_with_retries_post(
                http_session,
                f"{jira_entry.url}/rest/api/3/search/approximate-count",
                json_payload={"jql": test_jql}
            )

            if not count_response or not count_response.get('success'):
                my_logger.warning(f"Failed to get count for date range, using estimate: {count_response}")
                # Fall back to linear estimation
                ratio = i / num_batches
                mid_date = start_date + timedelta(seconds=ratio * total_seconds)
                break

            mid_count = count_response['result']['count']

            # Check if we're close enough to the target
            if abs(mid_count - target_records) < (target_per_batch * 0.1) or (
                    high_date - low_date).total_seconds() < 60:
                break

            if mid_count < target_records:
                low_date = mid_date
            else:
                high_date = mid_date

        batch_boundaries.append(mid_date)
        current_date = mid_date

    batch_boundaries.append(end_date)

    # Create JQL for each batch and validate it has records
    valid_batched_jqls = []

    for i in range(len(batch_boundaries) - 1):
        batch_start = batch_boundaries[i]
        batch_end = batch_boundaries[i + 1]

        batch_jql = create_date_range_jql(jql, batch_start, batch_end, field_start, field_end)

        # Verify this batch has records
        count_response = await fetch_with_retries_post(
            http_session,
            f"{jira_entry.url}/rest/api/3/search/approximate-count",
            json_payload={"jql": batch_jql}
        )

        if count_response and count_response.get('success'):
            batch_count = count_response['result']['count']
            if batch_count > 0:
                valid_batched_jqls.append(batch_jql)
                my_logger.info(f"Batch {i + 1}: {batch_count} records")
            else:
                my_logger.warning(f"Skipping batch {i + 1} with 0 records")

    return valid_batched_jqls


def create_date_range_jql(jql: str, start: datetime, end: datetime, field_start: str, field_end: str) -> str:
    """Create a JQL with the specified date range."""
    # Extract the original date range pattern from the JQL
    date_pattern = r'(created|updated)\s*>\s*[\'"]([^\'"]*)[\'"]\s*(?:and\s*(created|updated)\s*<\s*[\'"]([^\'"]*)[\'"]\s*)?'
    match = re.search(date_pattern, jql)

    if not match:
        return jql

    original_date_part = match.group(0)

    # Create the new date range
    new_date_range = f"{field_start} > '{start.strftime('%Y-%m-%d %H:%M')}' and {field_end} < '{end.strftime('%Y-%m-%d %H:%M')}'"

    # Replace the original date range with the new one
    return jql.replace(original_date_part, new_date_range)
    

def finalize_database_logging(logger_container_ref, session_manager, logger_names=None):
    handler = logger_container_ref.database_log_handler()
    handler.session_manager = session_manager
    handler._start_flush_thread()

    logger_names = logger_names or [logger_container_ref.config_file.Environment.Env()]
    for name in logger_names:
        logging.getLogger(name).addHandler(handler)


# rprint(container.my_factory())
# Attach listeners to the engine's pool
# event.listen(Pool, "checkout", before_checkout)
# event.listen(Pool, "invalidate", on_connection_invalidated)
# event.listen(Pool, "checkin", after_checkout, named=True)

# @listens_for(Pool, "connect")
# def my_on_connect(dbapi_con, connection_record):
#     print("New DBAPI connection:", dbapi_con)
#     print("Connection record", connection_record)
logging.captureWarnings(True)
# Global monitoring instances
global_system_metrics_collector = SystemMetricsCollector()
global_async_process_tracker = AsyncProcessTracker()

# Now import queue_processors after global_async_process_tracker is initialized
from dags.data_pipeline.queue_processors import (
    consume_changelog_enhanced as consume_changelog,
    consume_worklog_enhanced as consume_worklog,
    consume_comment_enhanced as consume_comment,
    consume_issue_links_enhanced as consume_issue_links,
    consume_issue_enhanced as consume_issue
)




# Global shutdown flag and event for graceful shutdown
# shutdown_requested = False
shutdown_event = asyncio.Event()

def request_shutdown():
    """Request a graceful shutdown"""
    # global shutdown_requested
    # shutdown_requested = True
    # shutdown_event.set()
    if not shutdown_event.is_set():
        shutdown_event.set()
    print("\n🛑 Shutdown requested. Stopping tasks gracefully...")

def setup_signal_handlers():
    def handler(signum, frame):
        print(f"\n🛑 Received signal {signum}. Initiating shutdown...")
        request_shutdown()

    for sig in (signal.SIGINT, signal.SIGTERM):
        signal.signal(sig, handler)

async def cancel_remaining_tasks(timeout=5):
    tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task() and not t.done()]
    if tasks:
        print(f"📋 Cancelling {len(tasks)} tasks...")
        for task in tasks:
            task.cancel()
        try:
            await asyncio.wait_for(asyncio.gather(*tasks, return_exceptions=True), timeout)
        except asyncio.TimeoutError:
            print("⏰ Timeout waiting for tasks to cancel. Force exiting...")


async def main():
    # Setup signal handlers
    asyncio.current_task().set_name("main")
    setup_signal_handlers()

    try:
        # Run your main logic here
        await process_task(True, projects=("plat",))

        print("\n✅ Main processing completed. Waiting for shutdown...")

        request_shutdown()
        await shutdown_event.wait()

    except asyncio.CancelledError:
        print("⚠️ Main task was cancelled.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        raise
    finally:
        await cancel_remaining_tasks()



if __name__ == '__main__':
    os.environ['DEBUG'] = "no-dataframe_utils"
    desired_width = 320
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', desired_width)
    np.set_printoptions(linewidth=desired_width)
    pd.set_option('display.max_columns', 10)

    # asyncio.get_event_loop().set_debug(True)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.set_debug(True)

    logging.getLogger('asyncio').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.pool').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.orm').setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.dialects.postgresql.asyncpg").setLevel(logging.WARNING)

    #aiohttp debug
    logging.getLogger("aiohttp.client").setLevel(logging.WARNING)
    logging.getLogger("aiohttp.connector").setLevel(logging.WARNING)

    # Only set the event loop policy if running on Windows
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # Print startup message
    print("🚀 Starting JIRA Data Pipeline...")
    print("💡 Press Ctrl+C to stop gracefully")
    print("💡 Alternative shutdown methods:")
    print("   - PowerShell: Get-Process -Name python | Stop-Process")
    print("   - Command Prompt: taskkill /F /IM python.exe")
    print("   - Task Manager: Find python.exe process and end it")
    print("=" * 50)

    logger_container = LoggerContainer()
    logger_container.wire(modules=["dags.data_pipeline.utility_code"])
    logger_container.init_resources()

    extract_container = IssueFieldsContainer()
    keepass_container = KeePassContainer()

    # Initialize DatabaseSessionManagerContainer with dependencies from KeePassContainer
    # database_container = DatabaseSessionManagerContainer()
    database_container = CoreSessionManagerContainer(logger_container=logger_container)
    # database_container.init_resources()
    database_container.pg_rw_entry.override(keepass_container.pg_rw)
    database_container.pg_ro_entry.override(keepass_container.pg_ro)
    database_container.schema.override('public')
    database_container.wire([__name__])

    jira_container = JiraEntryDetailsContainer()
    jira_container.wire([__name__])
    # jira_container.init_resources()
    jira_container.keepass.override(keepass_container.keepass_manager)

    application_container = ApplicationContainer()
    application_container.wire([__name__])
    enhanced_application_container = EnhancedApplicationContainer(logger_container=logger_container)

    enhanced_application_container.wire([__name__])

    queue_container = QueueContainer()
    queue_container.wire([__name__])
    queue_container.database_rw.override(application_container.database_rw())

    # Inject logger into DB log handler now that all dependencies are ready
    # finalize_database_logging(logger_container, database_container.database_rw())
    finalize_database_logging(logger_container, database_container.enhanced_session_manager_rw())

    try:
        # Run with proper signal handling
        asyncio.run(main())
    except asyncio.CancelledError:
        print("Main application was cancelled")
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user. Exiting...")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
